^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Changelog for package manipulation_msgs
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

0.2.1 (2016-07-19)
------------------
* Add maintainer (to receive ROS buildfarm error).
* Contributors: <PERSON>, <PERSON>

0.2.0 (2013-04-20)
------------------
* Merge pull request `#5 <https://github.com/ros-interactive-manipulation/manipulation_msgs/issues/5>`_ from mateiciocarlie/groovy-devel
  fixed comment in Grasp message
* fixed comment in Grasp message
* Contributors: Matei Ciocarlie

0.1.8 (2013-03-07)
------------------
* tagged 0.1.8 version
* Merge pull request `#4 <https://github.com/ros-interactive-manipulation/manipulation_msgs/issues/4>`_ from mateiciocarlie/groovy-devel
  fixed dependency in catkin_package
* fixed dependency in catkin_package
* Contributors: <PERSON><PERSON>

0.1.7 (2013-03-06 14:25)
------------------------
* tagged 0.1.7 version
* Merge pull request `#3 <https://github.com/ros-interactive-manipulation/manipulation_msgs/issues/3>`_ from vrabaud/patch-1
  Update package.xml
* Update package.xml
* Contributors: Matei Ciocarlie, Vincent Rabaud

0.1.6 (2013-03-06 09:34)
------------------------
* Merge branch 'groovy-devel' of github.com:ros-interactive-manipulation/manipulation_msgs into groovy-devel
* tagged version 0.1.6
* Merge pull request `#2 <https://github.com/ros-interactive-manipulation/manipulation_msgs/issues/2>`_ from mateiciocarlie/groovy-devel
  changes to port household to new architecture.
* changes to port household to new architecture. Added GraspPlanning srv and action. Reverted GraspableObject cluster back to PointCloud
* small updates to msgs
* Contributors: Ioan Sucan, Matei Ciocarlie

0.1.5 (2013-01-21 11:42)
------------------------
* bumped version number for release
* Contributors: Jonathan Binney

0.1.4 (2013-01-21 09:22)
------------------------
* Merge pull request `#1 <https://github.com/ros-interactive-manipulation/manipulation_msgs/issues/1>`_ from vrabaud/groovy-devel
  typo in the main CMake file
* fix typo
* some updates per discussions with matei
* add id to a Grasp
* add gripper translation to the grasp
* Contributors: Ioan Sucan, Kaijen Hsiao, Vincent Rabaud

0.1.3 (2013-01-05)
------------------
* added approach direction to the message
* Contributors: Ioan Sucan

0.1.2 (2012-12-31)
------------------
* adding Header and replacing movable_obstacles by allowed_touch_objects
* Contributors: Ioan Sucan

0.1.1 (2012-12-20 15:05)
------------------------

0.1.0 (2012-12-20 13:00)
------------------------
* small simplification
* initial commit
* Contributors: Ioan Sucan, Jonathan Binney
