<launch>
  <!-- Stomp Plugin for MoveIt -->
  <arg name="planning_plugin" value="stomp_moveit/StompPlannerManager" />

  <arg name="start_state_max_bounds_error" value="0.1" />
  <arg name="jiggle_fraction" value="0.05" />
  <!-- The request adapters (plugins) used when planning. ORDER MATTERS! -->
  <arg name="planning_adapters"
       default="default_planner_request_adapters/LimitMaxCartesianLinkSpeed
                default_planner_request_adapters/AddTimeParameterization
                default_planner_request_adapters/FixWorkspaceBounds
                default_planner_request_adapters/FixStartStateBounds
                default_planner_request_adapters/FixStartStateCollision
                default_planner_request_adapters/FixStartStatePathConstraints" />


  <param name="planning_plugin" value="$(arg planning_plugin)" />
  <param name="request_adapters" value="$(arg planning_adapters)" />
  <param name="start_state_max_bounds_error" value="$(arg start_state_max_bounds_error)" />
  <param name="jiggle_fraction" value="$(arg jiggle_fraction)" />

  <rosparam command="load" file="$(find piper_no_gripper_moveit)/config/stomp_planning.yaml"/>
</launch>
