# Piper机械臂 + 宇树Go1机器狗 仿真环境

这个包提供了Piper机械臂安装在宇树Go1机器狗上的完整仿真环境，支持RViz可视化和MoveIt运动规划。

## 功能特性

- **完整的机器人模型**: 包含简化的Go1机器狗底座和完整的Piper 6自由度机械臂
- **MoveIt集成**: 支持运动规划、避障和轨迹执行
- **RViz可视化**: 实时显示机器人状态和规划结果
- **Gazebo仿真**: 支持物理仿真环境（可选）

## 文件结构

```
piper_go1_moveit/
├── config/                    # MoveIt配置文件
│   ├── piper_go1.srdf        # 语义机器人描述
│   ├── joint_limits.yaml     # 关节限制
│   ├── kinematics.yaml       # 运动学配置
│   ├── ompl_planning.yaml    # OMPL规划器配置
│   └── ...
├── launch/                    # 启动文件
│   ├── demo.launch           # 主要演示启动文件
│   ├── test_rviz.launch      # 简单RViz测试
│   ├── demo_gazebo.launch    # Gazebo仿真启动
│   └── ...
└── scripts/                   # 测试脚本
    └── test_moveit.py        # MoveIt功能测试脚本
```

## 使用方法

### 1. 基本RViz可视化测试

启动简单的RViz可视化，查看机器人模型：

```bash
# 编译工作空间
cd /home/<USER>/piper_ros
catkin build
source devel/setup.bash

# 启动RViz可视化
roslaunch piper_go1_moveit test_rviz.launch
```

这将启动：
- 机器人模型加载
- joint_state_publisher_gui（可以手动控制关节）
- RViz可视化界面

### 2. MoveIt演示

启动完整的MoveIt演示环境：

```bash
# 启动MoveIt演示
roslaunch piper_go1_moveit demo.launch
```

这将启动：
- MoveIt move_group节点
- RViz with MoveIt插件
- 虚拟关节状态发布器
- 运动规划界面

在RViz中，您可以：
- 使用"MotionPlanning"插件进行交互式运动规划
- 设置目标姿态并规划路径
- 执行规划的轨迹
- 添加障碍物进行避障测试

### 3. Gazebo物理仿真（可选）

启动Gazebo物理仿真环境：

```bash
# 启动Gazebo仿真
roslaunch piper_go1_moveit demo_gazebo.launch
```

这将启动：
- Gazebo仿真环境
- 机器人物理模型
- ros_control控制器
- MoveIt与Gazebo的集成

### 4. 编程接口测试

运行Python测试脚本：

```bash
# 首先启动MoveIt演示
roslaunch piper_go1_moveit demo.launch

# 在新终端中运行测试脚本
cd /home/<USER>/piper_ros
source devel/setup.bash
rosrun piper_go1_moveit test_moveit.py
```

测试脚本将执行：
- 移动到预定义姿态（零位、home位）
- 关节空间运动规划
- 笛卡尔空间运动规划

## 机器人配置

### 规划组

- **arm**: 6自由度机械臂（joint1-joint6）
- **piper_arm**: 完整机械臂组（与arm相同）

### 预定义姿态

- **zero**: 所有关节角度为0的零位姿态
- **home**: 安全的初始姿态

### 坐标系

- **world**: 世界坐标系
- **go1_base_link**: Go1机器狗主体
- **base_link**: 机械臂基座
- **link6**: 机械臂末端执行器

## 注意事项

1. **简化模型**: Go1机器狗使用了简化的几何模型，主要用于可视化和基本避障
2. **固定底座**: 当前配置中Go1底座是固定的，不支持移动
3. **网格文件**: 确保piper_description包中的STL网格文件存在
4. **依赖项**: 需要安装MoveIt、RViz和相关的ROS包

## 故障排除

### 常见问题

1. **URDF解析错误**: 检查网格文件路径是否正确
2. **MoveIt规划失败**: 调整规划参数或检查关节限制
3. **RViz显示异常**: 确保正确设置Fixed Frame为"world"

### 调试命令

```bash
# 检查URDF文件
rosrun xacro xacro src/piper_description/urdf/piper_go1_combined.xacro

# 查看TF树
rosrun tf2_tools view_frames.py

# 检查关节状态
rostopic echo /joint_states
```

## 扩展功能

这个基础框架可以扩展以支持：
- Go1机器狗的移动控制
- 更复杂的传感器集成
- 多机器人协作
- 实际硬件接口

## 联系信息

如有问题或建议，请参考原始piper_ros项目文档或提交issue。
