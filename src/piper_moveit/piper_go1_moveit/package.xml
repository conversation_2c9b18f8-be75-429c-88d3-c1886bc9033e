<?xml version="1.0"?>
<package format="2">
  <name>piper_go1_moveit</name>
  <version>0.1.0</version>
  <description>MoveIt configuration for <PERSON> robotic arm mounted on Unit<PERSON> Go1 robot dog</description>

  <maintainer email="<EMAIL>">User</maintainer>
  <license>BSD</license>

  <buildtool_depend>catkin</buildtool_depend>

  <exec_depend>moveit_ros_move_group</exec_depend>
  <exec_depend>moveit_fake_controller_manager</exec_depend>
  <exec_depend>moveit_kinematics</exec_depend>
  <exec_depend>moveit_planners_ompl</exec_depend>
  <exec_depend>moveit_ros_visualization</exec_depend>
  <exec_depend>moveit_setup_assistant</exec_depend>
  <exec_depend>moveit_simple_controller_manager</exec_depend>
  <exec_depend>joint_state_publisher</exec_depend>
  <exec_depend>joint_state_publisher_gui</exec_depend>
  <exec_depend>robot_state_publisher</exec_depend>
  <exec_depend>rviz</exec_depend>
  <exec_depend>tf2_ros</exec_depend>
  <exec_depend>xacro</exec_depend>
  <exec_depend>piper_description</exec_depend>

  <export>
    <architecture_independent/>
  </export>
</package>
