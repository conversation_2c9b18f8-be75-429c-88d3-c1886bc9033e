<?xml version="1.0"?>
<launch>
  <!-- 简单的RViz测试launch文件，用于验证机械臂在Go1底座上的显示 -->
  
  <!-- Load the URDF into the ROS Parameter Server -->
  <param name="robot_description" command="$(find xacro)/xacro $(find piper_description)/urdf/piper_go1_combined.xacro" />

  <!-- Send fake joint values -->
  <node name="joint_state_publisher_gui" pkg="joint_state_publisher_gui" type="joint_state_publisher_gui">
    <param name="use_gui" value="true"/>
  </node>

  <!-- Combine joint values -->
  <node name="robot_state_publisher" pkg="robot_state_publisher" type="robot_state_publisher"/>

  <!-- Show in Rviz -->
  <node name="rviz" pkg="rviz" type="rviz" args="-d $(find piper_go1_moveit)/launch/test.rviz" required="true" />

</launch>
