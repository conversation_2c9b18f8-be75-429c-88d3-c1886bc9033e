<launch>

  <arg name="rviz_config" default="$(find piper_go1_moveit)/launch/moveit.rviz" />
  <arg name="debug" default="false" />
  <arg unless="$(arg debug)" name="launch_prefix" value="" />
  <arg     if="$(arg debug)" name="launch_prefix" value="gdb -x $(find piper_go1_moveit)/launch/gdb_settings.gdb --ex run --args" />

  <node name="$(anon rviz)" launch-prefix="$(arg launch_prefix)" pkg="rviz" type="rviz" respawn="false"
        args="-d $(arg rviz_config)" output="screen">
    <rosparam command="load" file="$(find piper_go1_moveit)/config/kinematics.yaml"/>
  </node>

</launch>
