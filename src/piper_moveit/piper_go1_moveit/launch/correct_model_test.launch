<?xml version="1.0"?>
<launch>
  <!-- 使用基于原始正确Piper代码的模型 -->
  
  <!-- Load the correct URDF with Go1 base -->
  <param name="robot_description" command="$(find xacro)/xacro $(find piper_description)/urdf/piper_with_go1_base.xacro" />

  <!-- Send fake joint values with GUI -->
  <node name="joint_state_publisher_gui" pkg="joint_state_publisher_gui" type="joint_state_publisher_gui">
    <param name="use_gui" value="true"/>
  </node>

  <!-- Combine joint values -->
  <node name="robot_state_publisher" pkg="robot_state_publisher" type="robot_state_publisher" respawn="true" output="screen"/>

  <!-- Show in Rviz with basic config -->
  <node name="rviz" pkg="rviz" type="rviz" args="-d $(find piper_go1_moveit)/launch/test.rviz" required="true" />

</launch>
