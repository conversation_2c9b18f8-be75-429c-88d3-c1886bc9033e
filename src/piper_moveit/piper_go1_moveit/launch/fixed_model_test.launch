<?xml version="1.0"?>
<launch>
  <!-- 使用修复后的简化几何体模型 -->
  
  <!-- Load the fixed simplified URDF into the ROS Parameter Server -->
  <param name="robot_description" command="$(find xacro)/xacro $(find piper_description)/urdf/piper_go1_simple.xacro" />

  <!-- Send fake joint values with GUI -->
  <node name="joint_state_publisher_gui" pkg="joint_state_publisher_gui" type="joint_state_publisher_gui">
    <param name="use_gui" value="true"/>
    <rosparam param="source_list">[/joint_states]</rosparam>
  </node>

  <!-- Combine joint values -->
  <node name="robot_state_publisher" pkg="robot_state_publisher" type="robot_state_publisher" respawn="true" output="screen">
    <remap from="/joint_states" to="/joint_states" />
  </node>

  <!-- Show in Rviz with basic config -->
  <node name="rviz" pkg="rviz" type="rviz" args="-d $(find piper_go1_moveit)/launch/test.rviz" required="true" />

</launch>
