#!/usr/bin/env python
# -*- coding: utf-8 -*-

import rospy
import moveit_commander
import moveit_msgs.msg
import geometry_msgs.msg
from math import pi
import sys

def test_piper_go1_moveit():
    """测试Piper机械臂在Go1底座上的MoveIt功能"""
    
    # 初始化moveit_commander和rospy
    moveit_commander.roscpp_initialize(sys.argv)
    rospy.init_node('test_piper_go1_moveit', anonymous=True)
    
    # 实例化RobotCommander对象，提供机器人的运动学模型和当前状态信息
    robot = moveit_commander.RobotCommander()
    
    # 实例化PlanningSceneInterface对象，提供远程接口获取和更新机器人周围环境信息
    scene = moveit_commander.PlanningSceneInterface()
    
    # 实例化MoveGroupCommander对象，用于控制机械臂规划和执行
    group_name = "arm"  # 或者使用 "piper_arm"
    move_group = moveit_commander.MoveGroupCommander(group_name)
    
    # 创建DisplayTrajectory发布者，用于在Rviz中显示轨迹
    display_trajectory_publisher = rospy.Publisher('/move_group/display_planned_path',
                                                   moveit_msgs.msg.DisplayTrajectory,
                                                   queue_size=20)
    
    # 获取基本信息
    planning_frame = move_group.get_planning_frame()
    print("============ Planning frame: %s" % planning_frame)
    
    eef_link = move_group.get_end_effector_link()
    print("============ End effector link: %s" % eef_link)
    
    group_names = robot.get_group_names()
    print("============ Available Planning Groups:", robot.get_group_names())
    
    print("============ Printing robot state")
    print(robot.get_current_state())
    print("")
    
    # 设置规划参数
    move_group.set_planning_time(10)
    move_group.set_num_planning_attempts(10)
    move_group.set_goal_position_tolerance(0.01)
    move_group.set_goal_orientation_tolerance(0.01)
    
    # 测试1: 移动到预定义的姿态
    print("============ 测试1: 移动到零位姿态")
    move_group.set_named_target("zero")
    plan = move_group.go(wait=True)
    move_group.stop()
    move_group.clear_pose_targets()
    
    if plan:
        print("成功移动到零位姿态")
    else:
        print("移动到零位姿态失败")
    
    rospy.sleep(2)
    
    # 测试2: 移动到home姿态
    print("============ 测试2: 移动到home姿态")
    move_group.set_named_target("home")
    plan = move_group.go(wait=True)
    move_group.stop()
    move_group.clear_pose_targets()
    
    if plan:
        print("成功移动到home姿态")
    else:
        print("移动到home姿态失败")
    
    rospy.sleep(2)
    
    # 测试3: 关节空间规划
    print("============ 测试3: 关节空间规划")
    joint_goal = move_group.get_current_joint_values()
    joint_goal[0] = 0.5
    joint_goal[1] = 0.3
    joint_goal[2] = -0.3
    joint_goal[3] = 0.2
    joint_goal[4] = 0.1
    joint_goal[5] = 0.0
    
    move_group.set_joint_value_target(joint_goal)
    plan = move_group.go(wait=True)
    move_group.stop()
    
    if plan:
        print("关节空间规划成功")
    else:
        print("关节空间规划失败")
    
    rospy.sleep(2)
    
    # 测试4: 笛卡尔空间规划
    print("============ 测试4: 笛卡尔空间规划")
    pose_goal = geometry_msgs.msg.Pose()
    pose_goal.orientation.w = 1.0
    pose_goal.position.x = 0.4
    pose_goal.position.y = 0.1
    pose_goal.position.z = 0.4
    
    move_group.set_pose_target(pose_goal)
    plan = move_group.go(wait=True)
    move_group.stop()
    move_group.clear_pose_targets()
    
    if plan:
        print("笛卡尔空间规划成功")
    else:
        print("笛卡尔空间规划失败")
    
    # 返回零位
    print("============ 返回零位")
    move_group.set_named_target("zero")
    move_group.go(wait=True)
    move_group.stop()
    
    print("============ 测试完成!")
    moveit_commander.roscpp_shutdown()

if __name__ == '__main__':
    try:
        test_piper_go1_moveit()
    except rospy.ROSInterruptException:
        pass
