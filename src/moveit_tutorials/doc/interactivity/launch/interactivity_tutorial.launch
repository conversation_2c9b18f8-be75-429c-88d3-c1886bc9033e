<launch>
  <arg name="arm_id" default="panda"/>

  <!-- load URDF -->
  <param name="robot_description" command="$(find xacro)/xacro '$(find panda_description)/robots/panda.urdf.xacro'" />

  <!-- load SRDF -->
  <param name="robot_description_semantic" textfile="$(find panda_moveit_config)/config/panda.srdf" />

  <!-- launch rviz -->
  <node name="$(anon rviz)" pkg="rviz" type="rviz" respawn="false" output="screen">
    <rosparam command="load" file="$(find panda_moveit_config)/config/kinematics.yaml" subst_value="true"/>
  </node>

  <!-- launch interactivity_tutorial -->
  <node name="interactivity_tutorial" pkg="moveit_tutorials" type="interactivity_tutorial" respawn="false" output="screen">
    <rosparam command="load" file="$(find panda_moveit_config)/config/kinematics.yaml" subst_value="true"/>
  </node>

</launch>
