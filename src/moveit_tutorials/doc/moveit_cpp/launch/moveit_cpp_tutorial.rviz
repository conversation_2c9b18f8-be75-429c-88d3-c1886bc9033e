Panels:
  - Class: rviz/Displays
    Help Height: 78
    Name: Displays
    Property Tree Widget:
      Expanded:
        - /Global Options1
        - /Status1
        - /PlanningScene1
      Splitter Ratio: 0.5
    Tree Height: 505
  - Class: rviz/Selection
    Name: Selection
  - Class: rviz/Tool Properties
    Expanded:
      - /2D Pose Estimate1
      - /2D Nav Goal1
      - /Publish Point1
    Name: Tool Properties
    Splitter Ratio: 0.588679016
  - Class: rviz/Views
    Expanded:
      - /Current View1
    Name: Views
    Splitter Ratio: 0.5
  - Class: rviz/Time
    Experimental: false
    Name: Time
    SyncMode: 0
    SyncSource: ""
  - Class: rviz_visual_tools/RvizVisualToolsGui
    Name: RvizVisualToolsGui
Toolbars:
  toolButtonStyle: 2
Visualization Manager:
  Class: ""
  Displays:
    - Alpha: 0.5
      Cell Size: 1
      Class: rviz/Grid
      Color: 160; 160; 164
      Enabled: true
      Line Style:
        Line Width: 0.0299999993
        Value: Lines
      Name: Grid
      Normal Cell Count: 0
      Offset:
        X: 0
        Y: 0
        Z: 0
      Plane: XY
      Plane Cell Count: 10
      Reference Frame: <Fixed Frame>
      Value: true
    - Attached Body Color: 150; 50; 150
      Class: moveit_rviz_plugin/RobotState
      Collision Enabled: false
      Enabled: false
      Links:
        All Links Enabled: true
        Expand Joint Details: false
        Expand Link Details: false
        Expand Tree: false
        Link Tree Style: Links in Alphabetic Order
      Name: RobotState
      Robot Alpha: 1
      Robot Description: robot_description
      Robot State Topic: display_robot_state
      Show All Links: true
      Show Highlights: true
      Value: false
      Visual Enabled: true
    - Class: moveit_rviz_plugin/PlanningScene
      Enabled: true
      Move Group Namespace: ""
      Name: PlanningScene
      Planning Scene Topic: /publish_planning_scene
      Robot Description: robot_description
      Scene Geometry:
        Scene Alpha: 0.899999976
        Scene Color: 50; 230; 50
        Scene Display Time: 0.200000003
        Show Scene Geometry: true
        Voxel Coloring: Z-Axis
        Voxel Rendering: Occupied Voxels
      Scene Robot:
        Attached Body Color: 150; 50; 150
        Links:
          All Links Enabled: true
          Expand Joint Details: false
          Expand Link Details: false
          Expand Tree: false
          Link Tree Style: Links in Alphabetic Order
          panda_hand:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
          panda_leftfinger:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
          panda_link0:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
          panda_link1:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
          panda_link2:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
          panda_link3:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
          panda_link4:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
          panda_link5:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
          panda_link6:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
          panda_link7:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
          panda_link8:
            Alpha: 1
            Show Axes: false
            Show Trail: false
          panda_rightfinger:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
        Robot Alpha: 1
        Show Robot Collision: false
        Show Robot Visual: true
      Value: true
    - Class: rviz/TF
      Enabled: false
      Frame Timeout: 15
      Frames:
        All Enabled: true
      Marker Scale: 1
      Name: TF
      Show Arrows: true
      Show Axes: true
      Show Names: true
      Tree:
        {}
      Update Interval: 0
      Value: false
    - Class: rviz/MarkerArray
      Enabled: true
      Marker Topic: /rviz_visual_tools
      Name: MarkerArray
      Namespaces:
        Text: true
      Queue Size: 100
      Value: true
  Enabled: true
  Global Options:
    Background Color: 48; 48; 48
    Default Light: true
    Fixed Frame: world
    Frame Rate: 30
  Name: root
  Tools:
    - Class: rviz/Interact
      Hide Inactive Objects: true
    - Class: rviz/MoveCamera
    - Class: rviz/Select
    - Class: rviz/FocusCamera
    - Class: rviz/Measure
    - Class: rviz/SetInitialPose
      Topic: /initialpose
    - Class: rviz/SetGoal
      Topic: /move_base_simple/goal
    - Class: rviz/PublishPoint
      Single click: true
      Topic: /clicked_point
  Value: true
  Views:
    Current:
      Class: rviz/Orbit
      Distance: 4.72797632
      Enable Stereo Rendering:
        Stereo Eye Separation: 0.0599999987
        Stereo Focal Distance: 1
        Swap Stereo Eyes: false
        Value: false
      Focal Point:
        X: 0.103620194
        Y: -0.190347269
        Z: 0.189688578
      Focal Shape Fixed Size: true
      Focal Shape Size: 0.0500000007
      Invert Z Axis: false
      Name: Current View
      Near Clip Distance: 0.00999999978
      Pitch: 0.415397704
      Target Frame: <Fixed Frame>
      Value: Orbit (rviz)
      Yaw: 0.575399756
    Saved: ~
Window Geometry:
  Displays:
    collapsed: false
  Height: 876
  Hide Left Dock: false
  Hide Right Dock: false
  QMainWindow State: 000000ff00000000fd000000040000000000000178000002dbfc0200000009fb0000001200530065006c0065006300740069006f006e00000001e10000009b0000006300fffffffb0000001e0054006f006f006c002000500072006f007000650072007400690065007302000001ed000001df00000185000000a3fb000000120056006900650077007300200054006f006f02000001df000002110000018500000122fb000000200054006f006f006c002000500072006f0070006500720074006900650073003203000002880000011d000002210000017afb000000100044006900730070006c00610079007301000000280000028d000000dc00fffffffb0000002000730065006c0065006300740069006f006e00200062007500660066006500720200000138000000aa0000023a00000294fb00000014005700690064006500530074006500720065006f02000000e6000000d2000003ee0000030bfb0000000c004b0069006e0065006300740200000186000001060000030c00000261fb00000024005200760069007a00560069007300750061006c0054006f006f006c007300470075006901000002bb000000480000004800ffffff000000010000010f000002dbfc0200000003fb0000001e0054006f006f006c002000500072006f00700065007200740069006500730100000041000000780000000000000000fb0000000a005600690065007700730100000028000002db000000b500fffffffb0000001200530065006c0065006300740069006f006e010000025a000000b200000000000000000000000200000490000000a9fc0100000001fb0000000a00560069006500770073030000004e00000080000002e100000197000000030000064000000042fc0100000002fb0000000800540069006d00650100000000000006400000027000fffffffb0000000800540069006d00650100000000000004500000000000000000000003ad000002db00000004000000040000000800000008fc0000000100000002000000010000000a0054006f006f006c00730100000000ffffffff0000000000000000
  RvizVisualToolsGui:
    collapsed: false
  Selection:
    collapsed: false
  Time:
    collapsed: false
  Tool Properties:
    collapsed: false
  Views:
    collapsed: false
  Width: 1600
  X: 0
  Y: 24
