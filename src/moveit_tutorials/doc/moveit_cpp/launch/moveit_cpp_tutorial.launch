<launch>
    <arg name="arm_id" default="panda"/>

    <!-- Specify the config files to use -->
    <rosparam ns="moveit_cpp_tutorial" command="load" file="$(find moveit_tutorials)/doc/moveit_cpp/config/moveit_cpp.yaml" />

    <!-- Planning Pipeline -->
    <include ns="/moveit_cpp_tutorial/ompl" file="$(find panda_moveit_config)/launch/ompl_planning_pipeline.launch.xml"/>

     <!-- Trajectory execution  -->
    <include ns="moveit_cpp_tutorial" file="$(find panda_moveit_config)/launch/trajectory_execution.launch.xml">
      <arg name="moveit_controller_manager" value="fake"/>
    </include>

    <!-- Load the URDF, SRDF and other .yaml configuration files on the param server -->
    <include file="$(find panda_moveit_config)/launch/planning_context.launch">
      <arg name="load_robot_description" value="true"/>
    </include>

    <!-- If needed, broadcast static tf for robot root -->
    <node pkg="tf2_ros" type="static_transform_publisher" name="virtual_joint_broadcaster_1" args="0 0 0 0 0 0 world panda_link0" />

    <!-- send fake joint values -->
    <node name="joint_state_publisher" pkg="joint_state_publisher" type="joint_state_publisher">
      <rosparam param="source_list">["/moveit_cpp_tutorial/fake_controller_joint_states"]</rosparam>
    </node>

    <!-- Start robot state publisher -->
    <node name="robot_state_publisher" pkg="robot_state_publisher" type="robot_state_publisher"/>

    <!-- Launch RViz -->
    <node name="$(anon rviz)" pkg="rviz" type="rviz" respawn="false"
      args="-d $(find moveit_tutorials)/doc/moveit_cpp/launch/moveit_cpp_tutorial.rviz" output="screen">
      <rosparam command="load" file="$(find panda_moveit_config)/config/kinematics.yaml" subst_value="true"/>
    </node>

    <include file="$(find moveit_tutorials)/doc/moveit_cpp/launch/moveit_cpp_node.launch"/>

</launch>
