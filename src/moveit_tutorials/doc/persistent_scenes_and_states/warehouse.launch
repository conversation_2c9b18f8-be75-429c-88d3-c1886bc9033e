<launch>

  <!-- The path to the database must be specified -->
  <arg name="moveit_warehouse_database_path" />

  <!-- Load warehouse parameters -->
  <include file="$(find moveit_resources_panda_moveit_config)/launch/warehouse_settings.launch.xml" />

## BEGIN_TUTORIAL
## Optionally, start the MongoDB Server (comment/uncomment the following in ``warehouse.launch``)
  <!-- <node name="$(anon mongo_wrapper_ros)" cwd="ROS_HOME" type="mongo_wrapper_ros.py" pkg="warehouse_ros_mongo">
    <param name="overwrite" value="false"/>
    <param name="database_path" value="$(arg moveit_warehouse_database_path)" />
  </node> -->
## END_TUTORIAL



</launch>
