<launch>
  <arg name="arm_id" default="piper"/>

  <!-- load URDF -->
  <param name="robot_description"
    command="$(find xacro)/xacro '$(find piper_description)/urdf/piper_description.xacro'"/>

  <!-- load SRDF -->
  <param name="robot_description_semantic" command="$(find xacro)/xacro '$(find piper_with_gripper_moveit)/config/piper_description.srdf'"/>

  <!-- Run RViz with a custom config -->
  <node name="$(anon rviz)" pkg="rviz" type="rviz" respawn="false"
        args="-d $(find moveit_tutorials)/doc/visualizing_collisions/launch/moveit.rviz" output="screen">
    <rosparam command="load" file="$(find piper_with_gripper_moveit)/config/kinematics.yaml" subst_value="true"/>
  </node>

  <!-- If needed, broadcast static tf2 for robot root -->
  <node pkg="tf2_ros" type="static_transform_publisher" name="virtual_joint_broadcaster_0" args="0 0 0 0 0 1 /world/base_link /base_link 10" />

  <!-- launch interactivity_tutorial -->
  <node name="visualizing_collisions_tutorial" pkg="moveit_tutorials" type="visualizing_collisions_tutorial" respawn="false" output="screen">
    <rosparam command="load" file="$(find piper_with_gripper_moveit)/config/kinematics.yaml" subst_value="true"/>
  </node>

</launch>
