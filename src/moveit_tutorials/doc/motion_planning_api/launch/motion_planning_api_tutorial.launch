<launch>
  <arg name="arm_id" default="piper"/>

  <node name="motion_planning_api_tutorial" pkg="moveit_tutorials" type="motion_planning_api_tutorial" respawn="false" output="screen">
    <rosparam command="load" file="$(find piper_with_gripper_moveit)/config/kinematics.yaml" subst_value="true"/>
    <param name="/planning_plugin" value="ompl_interface/OMPLPlanner"/>
    <rosparam command="load" file="$(find piper_with_gripper_moveit)/config/ompl_planning.yaml"/>
  </node>

</launch>
