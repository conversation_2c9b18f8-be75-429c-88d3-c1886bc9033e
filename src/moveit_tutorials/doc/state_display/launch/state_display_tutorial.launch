<launch>
  <arg name="arm_id" default="panda"/>

  <include file="$(find panda_moveit_config)/launch/planning_context.launch">
    <arg name="load_robot_description" value="true"/>
  </include>

  <node name="state_display_tutorial" pkg="moveit_tutorials" type="state_display_tutorial" respawn="false" output="screen">
    <rosparam command="load" file="$(find panda_moveit_config)/config/kinematics.yaml" subst_value="true"/>
  </node>
</launch>
