<?xml version="1.0"?>
<robot>
<gazebo reference="camera_depth_frame">
  <sensor name="kinect_camera" type="depth">
    <update_rate>20</update_rate>
    <camera>
      <horizontal_fov>1.047198</horizontal_fov>
      <image>
        <width>640</width>
        <height>480</height>
        <format>B8G8R8</format>
      </image>
      <clip>
        <near>0.05</near>
        <far>3</far>
      </clip>
    </camera>
    <plugin name="kinect_controller" filename="libgazebo_ros_openni_kinect.so">
      <baseline>0.1</baseline>
      <alwaysOn>true</alwaysOn>
      <updateRate>10</updateRate>
      <cameraName>camera_ir</cameraName>
      <imageTopicName>/camera/color/image_raw</imageTopicName>
      <cameraInfoTopicName>/camera/color/camera_info</cameraInfoTopicName>
      <depthImageTopicName>/camera/depth/image_raw</depthImageTopicName>
      <depthImageCameraInfoTopicName>/camera/depth/camera_info</depthImageCameraInfoTopicName>
      <pointCloudTopicName>/camera/depth/points</pointCloudTopicName>
      <frameName>camera_depth_optical_frame</frameName>
      <distortion_k1>0.0</distortion_k1>
      <distortion_k2>0.0</distortion_k2>
      <distortion_k3>0.0</distortion_k3>
      <distortion_t1>0.0</distortion_t1>
      <distortion_t2>0.0</distortion_t2>
      <pointCloudCutoff>0.16</pointCloudCutoff>
      <pointCloudCutoffMax>10.0</pointCloudCutoffMax>
    </plugin>
  </sensor>
</gazebo>
</robot>
