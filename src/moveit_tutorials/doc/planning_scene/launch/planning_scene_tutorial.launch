<launch>
  <arg name="arm_id" default="panda"/>

  <!-- send Panda urdf to parameter server -->
  <param name="robot_description"
    command="$(find xacro)/xacro '$(find franka_description)/robots/panda/panda.urdf.xacro' hand:=true"/>

  <include file="$(find panda_moveit_config)/launch/planning_context.launch"/>

  <node name="planning_scene_tutorial" pkg="moveit_tutorials" type="planning_scene_tutorial" respawn="false" output="screen">
    <rosparam command="load" file="$(find panda_moveit_config)/config/kinematics.yaml" subst_value="true"/>
  </node>
</launch>
