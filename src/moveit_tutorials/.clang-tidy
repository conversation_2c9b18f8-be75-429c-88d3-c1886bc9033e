---
Checks:          '-*,
                  performance-*,
                  llvm-namespace-comment,
                  modernize-redundant-void-arg,
                  modernize-use-nullptr,
                  modernize-use-default,
                  modernize-use-override,
                  modernize-loop-convert,
                  readability-named-parameter,
                  readability-redundant-smartptr-get,
                  readability-redundant-string-cstr,
                  readability-simplify-boolean-expr,
                  readability-container-size-empty,
                  readability-identifier-naming,
                  '
HeaderFilterRegex: ''
AnalyzeTemporaryDtors: false
CheckOptions:
  - key:             llvm-namespace-comment.ShortNamespaceLines
    value:           '10'
  - key:             llvm-namespace-comment.SpacesBeforeComments
    value:           '2'
  - key:             readability-braces-around-statements.ShortStatementLines
    value:           '2'
  # type names
  - key:             readability-identifier-naming.ClassCase
    value:           CamelCase
  - key:             readability-identifier-naming.EnumCase
    value:           CamelCase
  - key:             readability-identifier-naming.UnionCase
    value:           CamelCase
  # method names
  - key:             readability-identifier-naming.MethodCase
    value:           camelBack
...
