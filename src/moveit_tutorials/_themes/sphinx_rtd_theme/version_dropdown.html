<div class="version">
    <div class="version-dropdown">
        <lable for="version-list">Version:</lable>
    <select class="version-list" id="version-list" onchange="this.options[this.selectedIndex].value && (window.location = this.options[this.selectedIndex].value);">
        <option value="https://moveit.picknik.ai/humble/index.html">Moveit 2 - Humble</option>
        <option value="https://moveit.picknik.ai/main/index.html">Moveit 2 - Rolling</option>
        <option value='' selected>MoveIt 1 - {{ version }}</option>
        <option value="http://docs.ros.org/en/melodic/api/moveit_tutorials/html/index.html">MoveIt 1 - Melodic</option>
    </select>
    </div>
</div><br>
