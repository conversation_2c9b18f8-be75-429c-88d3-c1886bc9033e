<footer>
  {% if next or prev %}
    <div class="rst-footer-buttons" role="navigation" aria-label="footer navigation">
      {% if next %}
        <a href="{{ next.link|e }}" class="btn btn-neutral float-right" title="{{ next.title|striptags|e }}" accesskey="n">Next <span class="fa fa-arrow-circle-right"></span></a>
      {% endif %}
      {% if prev %}
        <a href="{{ prev.link|e }}" class="btn btn-neutral" title="{{ prev.title|striptags|e }}" accesskey="p"><span class="fa fa-arrow-circle-left"></span> Previous</a>
      {% endif %}
    </div>
  {% endif %}

  <hr/>

  <div role="contentinfo">
    <p>
    {%- if last_updated %}
      {% trans last_updated=last_updated|e %}Last updated on {{ last_updated }}.{% endtrans %}
    {%- endif %}
    </p>
  </div>

  {%- if show_sphinx %}
  {% trans %}Built with <a href="http://sphinx-doc.org/">Sphinx</a> using a <a href="https://github.com/snide/sphinx_rtd_theme">theme</a> provided by <a href="https://readthedocs.org">Read the Docs</a>{% endtrans %}.
  {%- endif %}

</footer>
