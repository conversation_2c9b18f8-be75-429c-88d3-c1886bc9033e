<div role="navigation" aria-label="breadcrumbs navigation">
  <ul class="wy-breadcrumbs">
    <li class="wy-breadcrumbs-aside">
      {% if pagename != "search" %}
        {% if display_github %}
          <a href="https://{{ github_host|default("github.com") }}/{{ github_user }}/{{ github_repo }}/blob/{{ github_version }}/{{ conf_py_path }}{{ pagename }}{{ source_suffix }}" class="fa fa-github"> Edit on GitHub</a>
        {% elif display_bitbucket %}
          <a href="https://bitbucket.org/{{ bitbucket_user }}/{{ bitbucket_repo }}/src/{{ bitbucket_version}}{{ conf_py_path }}{{ pagename }}{{ source_suffix }}" class="fa fa-bitbucket"> Edit on Bitbucket</a>
        {% elif show_source and source_url_prefix %}
          <a href="{{ source_url_prefix }}{{ pagename }}{{ source_suffix }}">View page source</a>
        {% elif show_source and has_source and sourcename %}
          <a href="{{ pathto('_sources/' + sourcename, true)|e }}" rel="nofollow"> View page source</a>
        {% endif %}
      {% endif %}
    </li>
    <li><a href="http://moveit.ros.org">MoveIt</a> &raquo;</li>
    <li><a href="{{ pathto(master_doc) }}">Tutorials</a> &raquo;</li>
      {% for doc in parents %}
          <li><a href="{{ doc.link|e }}">{{ doc.title }}</a> &raquo;</li>
      {% endfor %}
    <li>{{ title }}</li>
  </ul>
  {% include "moveit_version.html" %}
  <hr/>
</div>
