{# TEMPLATE VAR SETTINGS #}
{%- set url_root = pathto('', 1) %}
{%- if url_root == '#' %}{% set url_root = '' %}{% endif %}
{%- if not embedded and docstitle %}
  {%- set titlesuffix = " &mdash; "|safe + docstitle|e %}
{%- else %}
  {%- set titlesuffix = "" %}
{%- endif %}

<!DOCTYPE html>
<!--[if IE 8]><html class="no-js lt-ie9" lang="en" > <![endif]-->
<!--[if gt IE 8]><!--> <html class="no-js" lang="en" > <!--<![endif]-->
<head>
  <meta charset="utf-8">
  {{ metatags }}
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  {% block htmltitle %}
  <title>{{ title|striptags|e }}{{ titlesuffix }}</title>
  {% endblock %}

  {# FAVICON #}
  {% if favicon %}
    <link rel="shortcut icon" href="{{ pathto('_static/' + favicon, 1) }}"/>
  {% endif %}

  {# CSS #}

  {# OPENSEARCH #}
  {% if not embedded %}
    {% if use_opensearch %}
      <link rel="search" type="application/opensearchdescription+xml" title="{% trans docstitle=docstitle|e %}Search within {{ docstitle }}{% endtrans %}" href="{{ pathto('_static/opensearch.xml', 1) }}"/>
    {% endif %}

  {% endif %}

  {# RTD hosts this file, so just load on non RTD builds #}
  {% if not READTHEDOCS %}
    <link rel="stylesheet" href="{{ pathto('_static/' + style, 1) }}" type="text/css" />
  {% endif %}

  {% for cssfile in css_files %}
    <link rel="stylesheet" href="{{ pathto(cssfile, 1) }}" type="text/css" />
  {% endfor %}

  {% for cssfile in extra_css_files %}
    <link rel="stylesheet" href="{{ pathto(cssfile, 1) }}" type="text/css" />
  {% endfor %}

  {%- block linktags %}
    {%- if hasdoc('doc/about') %}
        <link rel="author" title="{{ _('About these documents') }}"
              href="{{ pathto('doc/about') }}"/>
    {%- endif %}
    {%- if hasdoc('genindex') %}
        <link rel="index" title="{{ _('Index') }}"
              href="{{ pathto('genindex') }}"/>
    {%- endif %}
    {%- if hasdoc('search') %}
        <link rel="search" title="{{ _('Search') }}" href="{{ pathto('search') }}"/>
    {%- endif %}
    {%- if hasdoc('copyright') %}
        <link rel="copyright" title="{{ _('Copyright') }}" href="{{ pathto('copyright') }}"/>
    {%- endif %}
    <link rel="top" title="{{ docstitle|e }}" href="{{ pathto('index') }}"/>
    {%- if parents %}
        <link rel="up" title="{{ parents[-1].title|striptags|e }}" href="{{ parents[-1].link|e }}"/>
    {%- endif %}
    {%- if next %}
        <link rel="next" title="{{ next.title|striptags|e }}" href="{{ next.link|e }}"/>
    {%- endif %}
    {%- if prev %}
        <link rel="prev" title="{{ prev.title|striptags|e }}" href="{{ prev.link|e }}"/>
    {%- endif %}
  {%- endblock %}
  {%- block extrahead %} {% endblock %}

  {# Keep modernizr in head - http://modernizr.com/docs/#installing #}
  <script src="{{ pathto('_static/js/modernizr.min.js', 1) }}"></script>

  <!-- Global site tag (gtag.js) - Google Analytics -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=UA-108532843-1"></script>
  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());

    gtag('config', 'UA-108532843-1');
  </script>

</head>

<body class="wy-body-for-nav" role="document">

  <div class="wy-grid-for-nav">

    {# SIDE NAV, TOGGLES ON MOBILE #}
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-nav-search">
        {% block sidebartitle %}

        <a href="http://moveit.ros.org">
          <img src="{{ pathto('_static/logo.png', 1) }}"/>
        </a>

        {% include "version_dropdown.html" %}
        {% include "searchbox.html" %}

        {% endblock %}
      </div>

      <div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="main navigation">
        {% block menu %}
          {% set toctree = toctree(maxdepth=4, collapse=False, includehidden=True) %}
          {% if toctree %}
              {{ toctree }}
          {% else %}
              <!-- Local TOC -->
              <div class="local-toc">{{ toc }}</div>
          {% endif %}
        {% endblock %}
      </div>
      &nbsp;
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap">

      {# MOBILE NAV, TRIGGLES SIDE NAV ON TOGGLE #}
      <nav class="wy-nav-top" role="navigation" aria-label="top navigation">
        <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
        <a href="{{ pathto(master_doc) }}">{{ project }}</a>
      </nav>

      {# PAGE CONTENT #}
      <div class="wy-nav-content">
        <div class="rst-content">
          {% include "breadcrumbs.html" %}
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             <!-- <div class="admonition note">  -->
               <!-- <p class="first admonition-title">Code Used in this Tutorial Available</p>  -->
               <!-- <p class="last">Code can be found at <a href = "https://github.com/moveit/moveit_tutorials">moveit_tutorials repository</a> in doc folder. Use master branch.</p> -->
             <!-- </div>  -->
            {% block body %}{% endblock %}
            {% if display_github %}
            <div class="admonition note">
              <p class="first admonition-title">Open Source Feedback</p>
              <p class="last">See something that needs improvement? Please open a pull request on this <a href="https://{{ github_host|default("github.com") }}/{{ github_user }}/{{ github_repo }}/blob/{{ github_version }}{{ conf_py_path }}/{{ pagename }}{{ source_suffix }}" class="fa fa-github"> GitHub page</a></p>
            </div>
            {% endif %}
           </div>
          </div>
          {% include "footer.html" %}
        </div>
      </div>

    </section>
  </div>

  {% if not embedded %}
    <script type="text/javascript">
        var DOCUMENTATION_OPTIONS = {
            URL_ROOT:'{{ url_root }}',
            VERSION:'{{ release|e }}',
            COLLAPSE_INDEX:false,
            FILE_SUFFIX:'{{ '' if no_search_suffix else file_suffix }}',
            HAS_SOURCE:  {{ has_source|lower }}
        };
    </script>
    {%- for scriptfile in script_files %}
      <script type="text/javascript" src="{{ pathto(scriptfile, 1) }}"></script>
    {%- endfor %}

  {% endif %}

  {# RTD hosts this file, so just load on non RTD builds #}
  {% if not READTHEDOCS %}
    <script type="text/javascript" src="{{ pathto('_static/js/theme.js', 1) }}"></script>
  {% endif %}

  {# STICKY NAVIGATION #}
  {% if theme_sticky_navigation %}
  <script type="text/javascript">
      jQuery(function () {
          SphinxRtdTheme.StickyNav.enable();
      });
  </script>
  {% endif %}

  {%- block footer %} {% endblock %}

</body>
</html>
