.icon:before {
  white-space: pre-wrap !important;
}

.header-override {
  margin-bottom: 12px;
}

.header-override img {
  width: 200px;
}

.wy-breadcrumbs li.wy-breadcrumbs-aside {
  display: block;
  width: 100%;
  text-align: right;
  margin-bottom: -25px;
}

.wy-body-for-nav .wy-side-nav-search {
  background-color: #33343f;
}

.red {
  color: red;
}

a {
  color: #005cfa;
}

a:hover {
  color: #9B58B6;
}

.rst-content .note {
  background-color: #ECECEC;
}

.rst-content .note .admonition-title {
  background-color: #777986;
}

.wy-menu {
  margin-top: 24px;
}

.wy-nav-side {
  background-color: #2D2E38;
}

.wy-side-nav-search {
  background-color: #33343F;
}

.wy-side-nav-search input[type=text] {
  border-radius: 4px;
  width: 200px;
  border-color: #fff;
}

.wy-side-nav-search img {
  margin: 12px auto 5px;
  width: 180px;
  height: auto;
  background-color: transparent;
  padding: 0;
  border-radius: 0;
}

.wy-side-nav-search span {
  font-size: 12px;
  color: rgba(255,255,255,0.4);
  text-transform: uppercase;
  display: block;
  text-align: center;
}

.wy-menu-vertical a {
  color: #b3b3b3;
}

.header-override p {
  margin-bottom: 0;
  text-align: right;
}

.caption-text {
  font-size: 12px;
  font-style: italic;
  font-weight: normal;
  line-height: normal;
  color: #404040;
}
