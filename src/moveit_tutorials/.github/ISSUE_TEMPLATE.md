### Description

Overview of your issue here.

### Your environment
* ROS Distro: [Indigo|Jade|Kinetic]
* OS Version: e.g. Ubuntu 16.04
* Source or Binary build?
* If binary, which release version?
* If source, which git commit or tag?

### Steps to reproduce
Tell us how to reproduce this issue. Attempt to provide a working demo, perhaps using Docker.

### Expected behaviour
Tell us what should happen

### Backtrace or Console output

Use [gist.github.com](gist.github.com) to copy-paste the console output or segfault backtrace using gdb.
