name: Build+Test+Deploy

on:
  workflow_dispatch:
  pull_request:
  push:
    branches:
      - master


permissions:
  contents: read
  pages: write
  id-token: write

jobs:
  doc:
    name: Build + Test + Deploy Website
    runs-on: ubuntu-latest
    container: ros:noetic-ros-base
    steps:
    - uses: actions/checkout@v4
    - name: Setup Pages
      uses: actions/configure-pages@v5
      if: github.repository_owner == 'moveit'
    - uses: ruby/setup-ruby@v1
      with:
        ruby-version: '3'
    - name: "Run htmlproofer.sh"
      run: |
        sudo apt-get update -q
        ./htmlproofer.sh

    - name: Upload pages artifact
      uses: actions/upload-pages-artifact@v3
      if: github.repository_owner == 'moveit'
      with:
        path: build/html

  deploy:
    if: github.repository_owner == 'moveit' && github.ref == 'refs/heads/master'
    runs-on: ubuntu-latest
    needs: doc
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}
    steps:
      - name: Deploy to GitHub Pages
        id: deployment
        uses: actions/deploy-pages@v4
