#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
轨迹数据查看器
功能：
1. 查看保存的轨迹数据
2. 重新绘制图表
3. 分析轨迹数据
"""

import os
import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import glob
from datetime import datetime
import csv

class TrajectoryDataViewer:
    def __init__(self, data_dir=None):
        """初始化数据查看器"""
        if data_dir is None:
            self.data_dir = os.path.expanduser("~/piper_trajectory_data")
        else:
            self.data_dir = data_dir
            
        print(f"数据目录: {self.data_dir}")
        
    def list_data_files(self):
        """列出所有数据文件"""
        if not os.path.exists(self.data_dir):
            print("数据目录不存在!")
            return
            
        joint_files = glob.glob(os.path.join(self.data_dir, "joint_data_*.csv"))
        pose_files = glob.glob(os.path.join(self.data_dir, "pose_data_*.csv"))
        
        print("\n=== 可用的数据文件 ===")
        print("关节数据文件:")
        for f in joint_files:
            print(f"  {os.path.basename(f)}")
            
        print("\n位姿数据文件:")
        for f in pose_files:
            print(f"  {os.path.basename(f)}")
            
        return joint_files, pose_files
        
    def load_csv_data(self, filename):
        """加载CSV数据"""
        data = {}
        with open(filename, 'r') as f:
            reader = csv.DictReader(f)
            for key in reader.fieldnames:
                data[key] = []

            for row in reader:
                for key, value in row.items():
                    try:
                        data[key].append(float(value))
                    except ValueError:
                        data[key].append(value)

        return data

    def load_latest_data(self):
        """加载最新的数据文件"""
        joint_files, pose_files = self.list_data_files()

        if not joint_files or not pose_files:
            print("没有找到数据文件!")
            return None, None

        # 获取最新的文件
        latest_joint = max(joint_files, key=os.path.getctime)
        latest_pose = max(pose_files, key=os.path.getctime)

        print(f"\n加载最新数据:")
        print(f"  关节数据: {os.path.basename(latest_joint)}")
        print(f"  位姿数据: {os.path.basename(latest_pose)}")

        # 读取数据
        joint_data = self.load_csv_data(latest_joint)
        pose_data = self.load_csv_data(latest_pose)

        print(f"  数据点数: {len(joint_data['Time'])}")
        print(f"  时间范围: {min(joint_data['Time']):.2f}s - {max(joint_data['Time']):.2f}s")

        return joint_data, pose_data
        
    def analyze_data(self, joint_data, pose_data):
        """分析轨迹数据"""
        print("\n=== 数据分析 ===")

        # 关节角度分析
        joint_cols = ['Joint1', 'Joint2', 'Joint3', 'Joint4', 'Joint5', 'Joint6']
        print("\n关节角度范围 (度):")
        for col in joint_cols:
            if col in joint_data:
                angles_deg = np.degrees(joint_data[col])
                print(f"  {col}: {angles_deg.min():7.2f}° ~ {angles_deg.max():7.2f}° (变化: {angles_deg.max()-angles_deg.min():6.2f}°)")

        # 末端执行器位置分析
        print("\n末端执行器位置范围 (m):")
        pos_cols = ['X', 'Y', 'Z']
        for col in pos_cols:
            if col in pose_data:
                pos = np.array(pose_data[col])
                print(f"  {col}: {pos.min():8.4f}m ~ {pos.max():8.4f}m (变化: {pos.max()-pos.min():7.4f}m)")

        # 轨迹长度计算
        if len(pose_data['X']) > 1:
            distances = []
            x_data = pose_data['X']
            y_data = pose_data['Y']
            z_data = pose_data['Z']

            for i in range(1, len(x_data)):
                dx = x_data[i] - x_data[i-1]
                dy = y_data[i] - y_data[i-1]
                dz = z_data[i] - z_data[i-1]
                dist = np.sqrt(dx**2 + dy**2 + dz**2)
                distances.append(dist)

            total_distance = sum(distances)
            time_data = joint_data['Time']
            print(f"\n轨迹总长度: {total_distance:.4f}m")
            print(f"平均速度: {total_distance/(max(time_data)-min(time_data)):.4f}m/s")
        
    def plot_joint_angles(self, joint_data):
        """绘制关节角度图"""
        plt.figure(figsize=(15, 10))

        joint_names = ['Joint 1', 'Joint 2', 'Joint 3', 'Joint 4', 'Joint 5', 'Joint 6']
        joint_cols = ['Joint1', 'Joint2', 'Joint3', 'Joint4', 'Joint5', 'Joint6']
        colors = ['red', 'blue', 'green', 'orange', 'purple', 'brown']

        for i, (name, col, color) in enumerate(zip(joint_names, joint_cols, colors)):
            if col in joint_data:
                plt.subplot(2, 3, i+1)
                angles_deg = np.degrees(joint_data[col])
                plt.plot(joint_data['Time'], angles_deg, color=color, linewidth=2)
                plt.title(f'{name} Angle vs Time')
                plt.xlabel('Time (s)')
                plt.ylabel('Angle (degrees)')
                plt.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.suptitle('Joint Angles During Circle Trajectory', fontsize=16, y=0.98)
        plt.show()
        
    def plot_3d_trajectory(self, pose_data):
        """绘制3D轨迹"""
        fig = plt.figure(figsize=(12, 8))
        ax = fig.add_subplot(111, projection='3d')

        # 绘制轨迹
        ax.plot(pose_data['X'], pose_data['Y'], pose_data['Z'],
                'b-', linewidth=3, label='Trajectory')

        # 标记起点和终点
        ax.scatter(pose_data['X'][0], pose_data['Y'][0], pose_data['Z'][0],
                  color='green', s=100, label='Start')
        ax.scatter(pose_data['X'][-1], pose_data['Y'][-1], pose_data['Z'][-1],
                  color='red', s=100, label='End')

        ax.set_xlabel('X (m)')
        ax.set_ylabel('Y (m)')
        ax.set_zlabel('Z (m)')
        ax.set_title('End-Effector 3D Trajectory')
        ax.legend()
        ax.grid(True, alpha=0.3)

        plt.show()

    def plot_yz_trajectory(self, pose_data):
        """绘制YZ平面轨迹"""
        plt.figure(figsize=(8, 8))
        plt.plot(pose_data['Y'], pose_data['Z'], 'b-', linewidth=3, label='Trajectory')
        plt.scatter(pose_data['Y'][0], pose_data['Z'][0], color='green', s=100, label='Start')
        plt.scatter(pose_data['Y'][-1], pose_data['Z'][-1], color='red', s=100, label='End')
        plt.xlabel('Y (m)')
        plt.ylabel('Z (m)')
        plt.title('End-Effector Trajectory (Y-Z Plane)')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.axis('equal')
        plt.show()
        
    def plot_joint_velocities(self, joint_data):
        """绘制关节角速度"""
        plt.figure(figsize=(15, 10))

        joint_names = ['Joint 1', 'Joint 2', 'Joint 3', 'Joint 4', 'Joint 5', 'Joint 6']
        joint_cols = ['Joint1', 'Joint2', 'Joint3', 'Joint4', 'Joint5', 'Joint6']
        colors = ['red', 'blue', 'green', 'orange', 'purple', 'brown']

        for i, (name, col, color) in enumerate(zip(joint_names, joint_cols, colors)):
            if col in joint_data:
                plt.subplot(2, 3, i+1)

                # 计算角速度 (数值微分)
                angles = np.array(joint_data[col])
                time = np.array(joint_data['Time'])
                velocities = np.gradient(angles, time)  # rad/s
                velocities_deg = np.degrees(velocities)  # deg/s

                plt.plot(time, velocities_deg, color=color, linewidth=2)
                plt.title(f'{name} Angular Velocity')
                plt.xlabel('Time (s)')
                plt.ylabel('Angular Velocity (deg/s)')
                plt.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.suptitle('Joint Angular Velocities During Circle Trajectory', fontsize=16, y=0.98)
        plt.show()
        
    def run_viewer(self):
        """运行查看器"""
        print("=== Piper机械臂轨迹数据查看器 ===")
        
        # 加载最新数据
        joint_data, pose_data = self.load_latest_data()
        if joint_data is None or pose_data is None:
            return
            
        # 分析数据
        self.analyze_data(joint_data, pose_data)
        
        # 交互式菜单
        while True:
            print("\n=== 选择操作 ===")
            print("1. 显示关节角度图")
            print("2. 显示3D轨迹图")
            print("3. 显示Y-Z平面轨迹图")
            print("4. 显示关节角速度图")
            print("5. 重新分析数据")
            print("6. 显示所有图表")
            print("0. 退出")
            
            try:
                choice = input("\n请选择 (0-6): ").strip()
                
                if choice == '0':
                    print("退出查看器")
                    break
                elif choice == '1':
                    self.plot_joint_angles(joint_data)
                elif choice == '2':
                    self.plot_3d_trajectory(pose_data)
                elif choice == '3':
                    self.plot_yz_trajectory(pose_data)
                elif choice == '4':
                    self.plot_joint_velocities(joint_data)
                elif choice == '5':
                    self.analyze_data(joint_data, pose_data)
                elif choice == '6':
                    print("显示所有图表...")
                    self.plot_joint_angles(joint_data)
                    self.plot_3d_trajectory(pose_data)
                    self.plot_yz_trajectory(pose_data)
                    self.plot_joint_velocities(joint_data)
                else:
                    print("无效选择，请重试")
                    
            except KeyboardInterrupt:
                print("\n\n退出查看器")
                break
            except Exception as e:
                print(f"错误: {e}")

def main():
    """主函数"""
    try:
        viewer = TrajectoryDataViewer()
        viewer.run_viewer()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序错误: {e}")

if __name__ == '__main__':
    main()
