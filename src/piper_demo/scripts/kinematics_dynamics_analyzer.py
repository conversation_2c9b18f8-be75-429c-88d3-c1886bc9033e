#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Piper机械臂运动学和动力学分析工具
功能：
1. 逆运动学分析
2. 逆动力学分析
3. 雅可比矩阵分析
4. 工作空间分析
5. 奇异性分析
"""

import os
import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import csv
import math
from datetime import datetime

class KinematicsDynamicsAnalyzer:
    def __init__(self, data_dir=None):
        """初始化分析器"""
        if data_dir is None:
            self.data_dir = os.path.expanduser("~/piper_trajectory_data")
        else:
            self.data_dir = data_dir
            
        # Piper机械臂DH参数 (需要根据实际机械臂调整)
        self.dh_params = {
            'a': [0, 0.105, 0.098, 0, 0, 0],      # 连杆长度
            'd': [0.1273, 0, 0, 0.0998, 0, 0.0607], # 连杆偏移
            'alpha': [np.pi/2, 0, 0, np.pi/2, -np.pi/2, 0]  # 连杆扭转角
        }
        
        print(f"运动学动力学分析器初始化完成")
        print(f"数据目录: {self.data_dir}")
        
    def load_csv_data(self, filename):
        """加载CSV数据"""
        data = {}
        with open(filename, 'r') as f:
            reader = csv.DictReader(f)
            for key in reader.fieldnames:
                data[key] = []
            
            for row in reader:
                for key, value in row.items():
                    try:
                        data[key].append(float(value))
                    except ValueError:
                        data[key].append(value)
        return data
        
    def load_latest_data(self):
        """加载最新数据"""
        import glob
        joint_files = glob.glob(os.path.join(self.data_dir, "joint_data_*.csv"))
        pose_files = glob.glob(os.path.join(self.data_dir, "pose_data_*.csv"))
        
        if not joint_files or not pose_files:
            print("没有找到数据文件!")
            return None, None
            
        latest_joint = max(joint_files, key=os.path.getctime)
        latest_pose = max(pose_files, key=os.path.getctime)
        
        joint_data = self.load_csv_data(latest_joint)
        pose_data = self.load_csv_data(latest_pose)
        
        return joint_data, pose_data
        
    def calculate_joint_velocities(self, joint_data):
        """计算关节角速度"""
        velocities = {}
        time = np.array(joint_data['Time'])
        
        joint_cols = ['Joint1', 'Joint2', 'Joint3', 'Joint4', 'Joint5', 'Joint6']
        for col in joint_cols:
            if col in joint_data:
                angles = np.array(joint_data[col])
                vel = np.gradient(angles, time)
                velocities[col] = vel
                
        return velocities
        
    def calculate_joint_accelerations(self, joint_data):
        """计算关节角加速度"""
        accelerations = {}
        time = np.array(joint_data['Time'])
        
        joint_cols = ['Joint1', 'Joint2', 'Joint3', 'Joint4', 'Joint5', 'Joint6']
        for col in joint_cols:
            if col in joint_data:
                angles = np.array(joint_data[col])
                vel = np.gradient(angles, time)
                acc = np.gradient(vel, time)
                accelerations[col] = acc
                
        return accelerations
        
    def analyze_inverse_kinematics(self, joint_data, pose_data):
        """逆运动学分析"""
        print("\n=== 逆运动学分析 ===")
        
        # 分析关节角度的连续性
        joint_cols = ['Joint1', 'Joint2', 'Joint3', 'Joint4', 'Joint5', 'Joint6']
        print("\n关节角度连续性分析:")
        
        for col in joint_cols:
            if col in joint_data:
                angles = np.array(joint_data[col])
                # 计算角度变化率
                angle_diff = np.diff(angles)
                max_jump = np.max(np.abs(angle_diff))
                print(f"  {col}: 最大跳跃 {np.degrees(max_jump):.2f}°")
                
        # 分析末端执行器轨迹误差
        print("\n末端执行器轨迹分析:")
        x_data = np.array(pose_data['X'])
        y_data = np.array(pose_data['Y'])
        z_data = np.array(pose_data['Z'])
        
        # 计算轨迹的圆度 (对于圆弧轨迹)
        center_y = (np.max(y_data) + np.min(y_data)) / 2
        center_z = (np.max(z_data) + np.min(z_data)) / 2
        
        distances = []
        for i in range(len(y_data)):
            dist = np.sqrt((y_data[i] - center_y)**2 + (z_data[i] - center_z)**2)
            distances.append(dist)
            
        distances = np.array(distances)
        radius_mean = np.mean(distances)
        radius_std = np.std(distances)
        
        print(f"  期望圆弧半径: {radius_mean:.4f}m")
        print(f"  半径标准差: {radius_std:.4f}m")
        print(f"  轨迹精度: {radius_std/radius_mean*100:.2f}%")
        
    def analyze_inverse_dynamics(self, joint_data):
        """逆动力学分析"""
        print("\n=== 逆动力学分析 ===")
        
        # 计算角速度和角加速度
        velocities = self.calculate_joint_velocities(joint_data)
        accelerations = self.calculate_joint_accelerations(joint_data)
        
        print("\n关节动力学参数分析:")
        joint_cols = ['Joint1', 'Joint2', 'Joint3', 'Joint4', 'Joint5', 'Joint6']
        
        for col in joint_cols:
            if col in velocities and col in accelerations:
                vel = velocities[col]
                acc = accelerations[col]
                
                max_vel = np.max(np.abs(vel))
                max_acc = np.max(np.abs(acc))
                rms_vel = np.sqrt(np.mean(vel**2))
                rms_acc = np.sqrt(np.mean(acc**2))
                
                print(f"  {col}:")
                print(f"    最大角速度: {np.degrees(max_vel):.2f}°/s")
                print(f"    最大角加速度: {np.degrees(max_acc):.2f}°/s²")
                print(f"    RMS角速度: {np.degrees(rms_vel):.2f}°/s")
                print(f"    RMS角加速度: {np.degrees(rms_acc):.2f}°/s²")
                
    def plot_kinematics_analysis(self, joint_data, pose_data):
        """绘制运动学分析图"""
        # 1. 关节空间轨迹图
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('逆运动学分析 - 关节空间轨迹', fontsize=16)
        
        joint_names = ['Joint 1', 'Joint 2', 'Joint 3', 'Joint 4', 'Joint 5', 'Joint 6']
        joint_cols = ['Joint1', 'Joint2', 'Joint3', 'Joint4', 'Joint5', 'Joint6']
        colors = ['red', 'blue', 'green', 'orange', 'purple', 'brown']
        
        time = np.array(joint_data['Time'])
        
        for i, (name, col, color) in enumerate(zip(joint_names, joint_cols, colors)):
            if col in joint_data:
                row, col_idx = i // 3, i % 3
                ax = axes[row, col_idx]
                
                angles_deg = np.degrees(joint_data[col])
                ax.plot(time, angles_deg, color=color, linewidth=2, label='实际轨迹')
                
                # 添加理想圆弧轨迹对比 (如果需要)
                ax.set_title(f'{name} 角度轨迹')
                ax.set_xlabel('时间 (s)')
                ax.set_ylabel('角度 (°)')
                ax.grid(True, alpha=0.3)
                ax.legend()
                
        plt.tight_layout()
        plt.show()
        
        # 2. 工作空间分析图
        fig = plt.figure(figsize=(15, 5))
        
        # XY平面投影
        ax1 = fig.add_subplot(131)
        ax1.plot(pose_data['X'], pose_data['Y'], 'b-', linewidth=2)
        ax1.scatter(pose_data['X'][0], pose_data['Y'][0], color='green', s=100, label='起点')
        ax1.scatter(pose_data['X'][-1], pose_data['Y'][-1], color='red', s=100, label='终点')
        ax1.set_xlabel('X (m)')
        ax1.set_ylabel('Y (m)')
        ax1.set_title('工作空间 - XY平面')
        ax1.grid(True, alpha=0.3)
        ax1.legend()
        ax1.axis('equal')
        
        # XZ平面投影
        ax2 = fig.add_subplot(132)
        ax2.plot(pose_data['X'], pose_data['Z'], 'g-', linewidth=2)
        ax2.scatter(pose_data['X'][0], pose_data['Z'][0], color='green', s=100, label='起点')
        ax2.scatter(pose_data['X'][-1], pose_data['Z'][-1], color='red', s=100, label='终点')
        ax2.set_xlabel('X (m)')
        ax2.set_ylabel('Z (m)')
        ax2.set_title('工作空间 - XZ平面')
        ax2.grid(True, alpha=0.3)
        ax2.legend()
        ax2.axis('equal')
        
        # YZ平面投影 (主要圆弧平面)
        ax3 = fig.add_subplot(133)
        ax3.plot(pose_data['Y'], pose_data['Z'], 'r-', linewidth=3, label='实际轨迹')
        ax3.scatter(pose_data['Y'][0], pose_data['Z'][0], color='green', s=100, label='起点')
        ax3.scatter(pose_data['Y'][-1], pose_data['Z'][-1], color='red', s=100, label='终点')
        
        # 添加理想圆弧对比
        y_data = np.array(pose_data['Y'])
        z_data = np.array(pose_data['Z'])
        center_y = (np.max(y_data) + np.min(y_data)) / 2
        center_z = (np.max(z_data) + np.min(z_data)) / 2
        radius = (np.max(y_data) - np.min(y_data)) / 2
        
        theta = np.linspace(0, 2*np.pi, 100)
        ideal_y = center_y + radius * np.cos(theta)
        ideal_z = center_z + radius * np.sin(theta)
        ax3.plot(ideal_y, ideal_z, 'k--', alpha=0.5, label='理想圆弧')
        
        ax3.set_xlabel('Y (m)')
        ax3.set_ylabel('Z (m)')
        ax3.set_title('工作空间 - YZ平面 (主轨迹)')
        ax3.grid(True, alpha=0.3)
        ax3.legend()
        ax3.axis('equal')
        
        plt.tight_layout()
        plt.show()

    def plot_dynamics_analysis(self, joint_data):
        """绘制动力学分析图"""
        # 计算速度和加速度
        velocities = self.calculate_joint_velocities(joint_data)
        accelerations = self.calculate_joint_accelerations(joint_data)

        time = np.array(joint_data['Time'])

        # 1. 角速度图
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('逆动力学分析 - 关节角速度', fontsize=16)

        joint_names = ['Joint 1', 'Joint 2', 'Joint 3', 'Joint 4', 'Joint 5', 'Joint 6']
        joint_cols = ['Joint1', 'Joint2', 'Joint3', 'Joint4', 'Joint5', 'Joint6']
        colors = ['red', 'blue', 'green', 'orange', 'purple', 'brown']

        for i, (name, col, color) in enumerate(zip(joint_names, joint_cols, colors)):
            if col in velocities:
                row, col_idx = i // 3, i % 3
                ax = axes[row, col_idx]

                vel_deg = np.degrees(velocities[col])
                ax.plot(time, vel_deg, color=color, linewidth=2)
                ax.set_title(f'{name} 角速度')
                ax.set_xlabel('时间 (s)')
                ax.set_ylabel('角速度 (°/s)')
                ax.grid(True, alpha=0.3)

                # 添加统计信息
                max_vel = np.max(np.abs(vel_deg))
                ax.text(0.02, 0.98, f'最大: {max_vel:.1f}°/s',
                       transform=ax.transAxes, verticalalignment='top',
                       bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))

        plt.tight_layout()
        plt.show()

    def run_analysis(self):
        """运行完整分析"""
        print("=== Piper机械臂运动学动力学分析器 ===")

        # 加载数据
        joint_data, pose_data = self.load_latest_data()
        if joint_data is None or pose_data is None:
            return

        print(f"数据加载完成: {len(joint_data['Time'])} 个数据点")

        # 运行分析
        self.analyze_inverse_kinematics(joint_data, pose_data)
        self.analyze_inverse_dynamics(joint_data)

        # 交互式菜单
        while True:
            print("\n=== 分析选项 ===")
            print("1. 显示运动学分析图")
            print("2. 显示动力学分析图")
            print("3. 重新运行分析")
            print("0. 退出")

            try:
                choice = input("\n请选择 (0-3): ").strip()

                if choice == '0':
                    print("退出分析器")
                    break
                elif choice == '1':
                    self.plot_kinematics_analysis(joint_data, pose_data)
                elif choice == '2':
                    self.plot_dynamics_analysis(joint_data)
                elif choice == '3':
                    self.analyze_inverse_kinematics(joint_data, pose_data)
                    self.analyze_inverse_dynamics(joint_data)
                else:
                    print("无效选择，请重试")

            except KeyboardInterrupt:
                print("\n\n退出分析器")
                break
            except Exception as e:
                print(f"错误: {e}")

def main():
    """主函数"""
    try:
        analyzer = KinematicsDynamicsAnalyzer()
        analyzer.run_analysis()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序错误: {e}")

if __name__ == '__main__':
    main()
