#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
关节数据分析工具
用于分析为什么某些关节角度曲线看起来像直线
"""

import numpy as np
import os
import glob
import csv

def analyze_latest_execution():
    """分析最新的执行数据"""
    data_dir = os.path.expanduser("~/piper_trajectory_data")

    # 找到最新的数据文件
    data_files = glob.glob(os.path.join(data_dir, "execution_data_*.csv"))
    if not data_files:
        print("❌ 没有找到数据文件")
        return

    latest_file = max(data_files, key=os.path.getctime)
    print(f"📁 分析文件: {os.path.basename(latest_file)}")

    # 读取数据
    data = {}
    with open(latest_file, 'r') as f:
        reader = csv.DictReader(f)
        for key in reader.fieldnames:
            data[key] = []

        for row in reader:
            for key, value in row.items():
                try:
                    data[key].append(float(value))
                except ValueError:
                    data[key].append(value)
    
    print("\n" + "="*80)
    print("关节运动分析报告")
    print("="*80)
    
    joint_names = ['Joint1', 'Joint2', 'Joint3', 'Joint4', 'Joint5', 'Joint6']
    
    for joint in joint_names:
        if joint in data:
            angles_rad = np.array(data[joint])
            angles_deg = np.degrees(angles_rad)
            
            # 计算统计信息
            min_angle = np.min(angles_deg)
            max_angle = np.max(angles_deg)
            range_angle = max_angle - min_angle
            std_angle = np.std(angles_deg)
            
            # 判断是否有显著运动
            if range_angle > 1.0:  # 变化超过1度
                motion_status = "✅ 有显著运动"
            elif range_angle > 0.1:  # 变化超过0.1度
                motion_status = "⚠️  有轻微运动"
            else:
                motion_status = "❌ 几乎无运动"
            
            print(f"\n{joint}:")
            print(f"  角度范围: {range_angle:.4f}° ({min_angle:.4f}° 到 {max_angle:.4f}°)")
            print(f"  标准差: {std_angle:.4f}°")
            print(f"  运动状态: {motion_status}")
            
            # 如果变化很小，解释原因
            if range_angle < 0.1:
                print(f"  📝 说明: 此关节在执行过程中基本保持静止")
                print(f"       这就是为什么图表中显示为直线的原因")
    
    print("\n" + "="*80)
    print("总结:")
    print("- 如果关节角度变化很小(<0.1°)，图表会显示为直线")
    print("- 这是正常现象，表示该关节在此次运动中没有参与")
    print("- 要看到明显的曲线，需要规划涉及更多关节运动的轨迹")
    print("="*80)

if __name__ == '__main__':
    analyze_latest_execution()
