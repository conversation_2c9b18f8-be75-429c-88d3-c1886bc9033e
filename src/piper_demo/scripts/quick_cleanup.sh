#!/bin/bash

# 快速清理脚本 - 解决终端变慢问题

echo "🔧 快速清理系统..."

# 1. 停止可能的图形进程
echo "停止图形进程..."
pkill -f "matplotlib" 2>/dev/null || true
pkill -f "python.*plot" 2>/dev/null || true

# 2. 清理Python缓存
echo "清理Python缓存..."
find /home/<USER>"*.pyc" -delete 2>/dev/null || true
find /home/<USER>"__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true

# 3. 清理临时文件
echo "清理临时文件..."
rm -rf /tmp/matplotlib* 2>/dev/null || true
rm -rf /tmp/ros_* 2>/dev/null || true

# 4. 重置环境变量
echo "重置环境变量..."
export PYTHONDONTWRITEBYTECODE=1
export MPLBACKEND=Agg

# 5. 清理内存 (如果有权限)
echo "清理内存缓存..."
sync
echo 3 | sudo tee /proc/sys/vm/drop_caches > /dev/null 2>&1 || echo "需要sudo权限清理内存缓存"

echo "✅ 快速清理完成!"
echo ""
echo "建议操作:"
echo "1. 重新打开终端"
echo "2. 或者运行: source ~/.bashrc"
echo ""
