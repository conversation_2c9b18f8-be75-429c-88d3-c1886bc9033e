#!/bin/bash

# Piper机械臂圆弧轨迹演示启动脚本 - 带可视化版本

echo "=== Piper机械臂圆弧轨迹演示 - 带可视化版本 ==="
echo ""

# 检查ROS环境
if [ -z "$ROS_PACKAGE_PATH" ]; then
    echo "错误: ROS环境未设置"
    echo "请先运行: source /opt/ros/noetic/setup.bash"
    exit 1
fi

# 检查工作空间
WORKSPACE_DIR="/home/<USER>/piper_ros"
if [ ! -d "$WORKSPACE_DIR" ]; then
    echo "错误: 工作空间目录不存在: $WORKSPACE_DIR"
    exit 1
fi

cd $WORKSPACE_DIR

# 检查是否已编译
if [ ! -f "devel/setup.bash" ]; then
    echo "错误: 工作空间未编译"
    echo "请先运行: catkin_make"
    exit 1
fi

# 设置环境
source devel/setup.bash

echo "1. 检查ROS Master..."
if ! rostopic list &>/dev/null; then
    echo "错误: ROS Master未运行"
    echo "请先启动MoveIt demo:"
    echo "  roslaunch piper_no_gripper_moveit demo.launch"
    exit 1
fi

echo "2. 检查MoveIt服务..."
if ! rosservice list | grep -q move_group; then
    echo "错误: MoveIt服务未运行"
    echo "请先启动MoveIt demo:"
    echo "  roslaunch piper_no_gripper_moveit demo.launch"
    exit 1
fi

echo "3. 启动圆弧轨迹演示..."
echo "   - 将执行完整的圆弧轨迹"
echo "   - 记录关节角度和末端执行器位置"
echo "   - 生成可视化图表"
echo "   - 保存数据到CSV文件"
echo ""

# 运行演示程序
rosrun piper_demo circle_demo_with_plot.py

echo ""
echo "=== 演示完成 ==="
echo "数据已保存到: ~/piper_trajectory_data/"
echo ""
echo "要查看轨迹数据，请运行:"
echo "  rosrun piper_demo view_trajectory_data.py"
echo ""
