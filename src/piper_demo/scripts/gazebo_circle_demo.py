#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Piper机械臂圆弧轨迹演示程序 - Gazebo仿真版本
功能：
1. 在Gazebo中控制Piper机械臂执行圆弧轨迹
2. 实时输出关节角度
3. 可视化轨迹规划和执行过程
"""

import rospy
import sys
import moveit_commander
from moveit_commander import MoveGroupCommander, PlanningSceneInterface
from geometry_msgs.msg import Pose, PoseStamped, Point
from sensor_msgs.msg import JointState
from copy import deepcopy
import moveit_msgs.msg
import math
import numpy as np
import time

class GazeboCircleDemo:
    def __init__(self):
        """初始化Gazebo圆弧演示类"""
        # 初始化moveit_commander和rospy节点
        moveit_commander.roscpp_initialize(sys.argv)
        rospy.init_node('gazebo_circle_demo', anonymous=True)
        
        # 初始化机械臂规划组
        self.arm = MoveGroupCommander('arm')
        self.arm.set_planning_time(10)
        self.arm.set_num_planning_attempts(5)
        self.arm.set_max_velocity_scaling_factor(0.3)
        self.arm.set_max_acceleration_scaling_factor(0.3)
        
        # 初始化场景
        self.scene = PlanningSceneInterface()
        
        # 设置参考坐标系
        self.arm.set_pose_reference_frame('base_link')
        
        # 获取末端执行器链接名称
        self.end_effector_link = self.arm.get_end_effector_link()
        
        # 订阅关节状态
        self.joint_sub = rospy.Subscriber('/joint_states', JointState, self.joint_state_callback)
        self.current_joint_states = None
        
        # 发布轨迹显示
        self.display_pub = rospy.Publisher('/move_group/display_planned_path',
                                         moveit_msgs.msg.DisplayTrajectory,
                                         queue_size=20)
        
        rospy.loginfo("=== Gazebo Circle Demo Initialized ===")
        rospy.loginfo("Planning group: %s", self.arm.get_name())
        rospy.loginfo("End effector: %s", self.end_effector_link)
        rospy.loginfo("Reference frame: %s", self.arm.get_planning_frame())
        
        # 等待关节状态
        rospy.loginfo("Waiting for joint states...")
        while self.current_joint_states is None and not rospy.is_shutdown():
            rospy.sleep(0.1)
        rospy.loginfo("Joint states received!")
        
    def joint_state_callback(self, msg):
        """关节状态回调函数"""
        self.current_joint_states = msg
        
    def print_joint_angles(self, title="Current Joint Angles"):
        """打印当前关节角度"""
        if self.current_joint_states is None:
            rospy.logwarn("No joint states available")
            return
            
        rospy.loginfo("=== %s ===", title)
        joint_names = ['joint1', 'joint2', 'joint3', 'joint4', 'joint5', 'joint6']
        
        for i, joint_name in enumerate(joint_names):
            if joint_name in self.current_joint_states.name:
                idx = self.current_joint_states.name.index(joint_name)
                angle_rad = self.current_joint_states.position[idx]
                angle_deg = math.degrees(angle_rad)
                rospy.loginfo("%s: %.4f rad (%.2f°)", joint_name, angle_rad, angle_deg)
        rospy.loginfo("=" * 40)
        
    def move_to_start_position(self):
        """移动到起始位置"""
        rospy.loginfo("Moving to start position...")

        # 设置起始关节角度 (单位：弧度) - 更保守的角度
        start_joint_angles = [0.0, 0.3, -0.5, 0.0, 0.2, 0.0]

        self.arm.set_joint_value_target(start_joint_angles)
        success = self.arm.go(wait=True)
        self.arm.stop()

        if success:
            rospy.loginfo("Successfully moved to start position")
            rospy.sleep(2)  # 等待稳定
            self.print_joint_angles("Start Position Joint Angles")
            return True
        else:
            rospy.logerr("Failed to move to start position")
            return False
            
    def generate_circle_waypoints(self, center, radius, num_points=20):
        """生成圆弧轨迹路径点"""
        waypoints = []
        
        # 获取当前位姿作为起始点
        current_pose = self.arm.get_current_pose().pose
        start_pose = deepcopy(current_pose)
        
        rospy.loginfo("Circle parameters:")
        rospy.loginfo("  Center: (%.3f, %.3f, %.3f)", center[0], center[1], center[2])
        rospy.loginfo("  Radius: %.3f m", radius)
        rospy.loginfo("  Points: %d", num_points)
        
        # 生成圆弧上的点 (在YZ平面内) - 只生成半圆
        for i in range(num_points + 1):
            angle = math.pi * i / num_points  # 只画半圆 (0 到 π)

            waypoint = deepcopy(start_pose)
            waypoint.position.x = center[0]  # X坐标保持不变
            waypoint.position.y = center[1] + radius * math.cos(angle)
            waypoint.position.z = center[2] + radius * math.sin(angle)

            # 保持末端执行器姿态不变
            waypoint.orientation = start_pose.orientation

            waypoints.append(waypoint)
            
        rospy.loginfo("Generated %d waypoints for circle trajectory", len(waypoints))
        return waypoints
        
    def execute_circle_trajectory(self):
        """执行圆弧轨迹"""
        rospy.loginfo("Starting circle trajectory execution...")
        
        # 获取当前位置
        current_pose = self.arm.get_current_pose().pose
        rospy.loginfo("Current end-effector position:")
        rospy.loginfo("  X: %.3f, Y: %.3f, Z: %.3f", 
                     current_pose.position.x, 
                     current_pose.position.y, 
                     current_pose.position.z)
        
        # 定义圆弧参数 - 使用更小的半径和更少的点
        center = [current_pose.position.x,
                 current_pose.position.y,
                 current_pose.position.z]
        radius = 0.03  # 3cm半径，更小更安全

        # 生成圆弧路径点 - 只画半圆，减少复杂度
        waypoints = self.generate_circle_waypoints(center, radius, 8)
        
        # 规划笛卡尔路径
        rospy.loginfo("Planning cartesian path...")
        (plan, fraction) = self.arm.compute_cartesian_path(
            waypoints,   # 路径点
            0.01)        # 步长 (1cm)
        
        rospy.loginfo("Path planning result:")
        rospy.loginfo("  Success rate: %.1f%%", fraction * 100)
        rospy.loginfo("  Trajectory points: %d", len(plan.joint_trajectory.points))
        
        if fraction > 0.8:  # 如果成功率超过80%
            # 显示规划的轨迹
            display_trajectory = moveit_msgs.msg.DisplayTrajectory()
            display_trajectory.trajectory_start = self.arm.get_current_state()
            display_trajectory.trajectory.append(plan)
            self.display_pub.publish(display_trajectory)
            
            rospy.loginfo("Executing circle trajectory...")
            self.print_joint_angles("Before Execution")
            
            # 执行轨迹
            success = self.arm.execute(plan, wait=True)
            
            if success:
                rospy.loginfo("Circle trajectory executed successfully!")
                rospy.sleep(1)  # 等待稳定
                self.print_joint_angles("After Execution")
                return True
            else:
                rospy.logerr("Failed to execute circle trajectory")
                return False
        else:
            rospy.logwarn("Path planning success rate too low: %.1f%%", fraction * 100)
            return False
            
    def run_demo(self):
        """运行演示程序"""
        try:
            rospy.loginfo("=== Starting Gazebo Circle Demo ===")
            
            # 步骤1: 移动到起始位置
            if not self.move_to_start_position():
                return False
                
            rospy.sleep(2)  # 等待2秒
            
            # 步骤2: 执行圆弧轨迹
            if not self.execute_circle_trajectory():
                return False
                
            rospy.loginfo("=== Demo completed successfully! ===")
            return True
            
        except rospy.ROSInterruptException:
            rospy.loginfo("Demo interrupted by user")
            return False
        except Exception as e:
            rospy.logerr("Demo failed with error: %s", str(e))
            return False
            
    def shutdown(self):
        """关闭演示程序"""
        rospy.loginfo("Shutting down Gazebo Circle Demo...")
        moveit_commander.roscpp_shutdown()

def main():
    """主函数"""
    try:
        # 创建演示对象
        demo = GazeboCircleDemo()
        
        # 运行演示
        success = demo.run_demo()
        
        if success:
            rospy.loginfo("Demo completed successfully!")
        else:
            rospy.logerr("Demo failed!")
            
        # 关闭
        demo.shutdown()
        
    except KeyboardInterrupt:
        rospy.loginfo("Demo interrupted by user")
    except Exception as e:
        rospy.logerr("Demo failed: %s", str(e))

if __name__ == '__main__':
    main()
