#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Piper机械臂关节监控器 - 带力矩估算版本
功能：
1. 实时输出关节角度、角速度、角加速度
2. 基于动力学模型估算关节力矩
3. 数据记录和可视化
4. 支持MoveIt+Gazebo仿真环境
"""

import rospy
import numpy as np
from sensor_msgs.msg import JointState
import math
import time
from datetime import datetime
import os

class JointMonitorWithTorque:
    def __init__(self):
        """初始化关节监控器"""
        rospy.init_node('joint_monitor_with_torque', anonymous=True)
        
        # 关节名称（只监控前6个关节）
        self.joint_names = ['joint1', 'joint2', 'joint3', 'joint4', 'joint5', 'joint6']
        self.num_joints = len(self.joint_names)
        
        # 当前状态
        self.current_positions = np.zeros(self.num_joints)
        self.current_velocities = np.zeros(self.num_joints)
        self.current_accelerations = np.zeros(self.num_joints)
        self.current_efforts = np.zeros(self.num_joints)
        self.estimated_torques = np.zeros(self.num_joints)
        self.prev_velocities = np.zeros(self.num_joints)
        self.prev_time = None
        
        # 机械臂动力学参数 (简化模型)
        self.link_masses = [2.0, 3.5, 2.8, 1.5, 1.0, 0.5]  # kg
        self.link_lengths = [0.1273, 0.105, 0.098, 0.0998, 0.06, 0.0607]  # m
        self.link_coms = [0.05, 0.05, 0.05, 0.03, 0.03, 0.02]  # 质心位置 m
        self.gravity = 9.81  # m/s²
        
        # 数据记录
        self.recording = False
        self.recorded_data = []
        self.start_time = rospy.get_time()
        
        # 统计数据
        self.data_count = 0
        self.max_positions = np.full(self.num_joints, -float('inf'))
        self.min_positions = np.full(self.num_joints, float('inf'))
        self.max_velocities = np.zeros(self.num_joints)
        self.max_accelerations = np.zeros(self.num_joints)
        self.max_torques = np.zeros(self.num_joints)
        
        # 订阅Gazebo关节状态
        self.joint_sub = rospy.Subscriber('/gazebo/joint_states', JointState, self.joint_state_callback)
        
        rospy.loginfo("=== 关节监控器(带力矩估算)已启动 ===")
        rospy.loginfo("监控关节: %s", self.joint_names)
        
    def joint_state_callback(self, msg):
        """关节状态回调函数"""
        current_time = rospy.get_time()
        
        # 提取前6个关节的数据
        for i, joint_name in enumerate(self.joint_names):
            if joint_name in msg.name:
                idx = msg.name.index(joint_name)
                
                # 位置
                self.current_positions[i] = msg.position[idx]
                
                # 速度
                if len(msg.velocity) > idx:
                    self.current_velocities[i] = msg.velocity[idx]
                else:
                    self.current_velocities[i] = 0.0
                
                # 力矩 (来自Gazebo)
                if len(msg.effort) > idx:
                    self.current_efforts[i] = msg.effort[idx]
                else:
                    self.current_efforts[i] = 0.0
        
        # 计算加速度
        if self.prev_time is not None:
            dt = current_time - self.prev_time
            if dt > 0:
                self.current_accelerations = (self.current_velocities - self.prev_velocities) / dt
        
        # 估算关节力矩
        self.estimate_joint_torques()
        
        # 更新统计数据
        self.update_statistics()
        
        # 记录数据
        if self.recording:
            self.record_data(current_time)
        
        # 更新前一次的数据
        self.prev_velocities = self.current_velocities.copy()
        self.prev_time = current_time
        self.data_count += 1
        
    def estimate_joint_torques(self):
        """估算关节力矩 (简化动力学模型)"""
        q = self.current_positions
        qd = self.current_velocities
        qdd = self.current_accelerations
        
        # 简化的重力补偿计算
        for i in range(self.num_joints):
            # 重力力矩 (简化计算)
            gravity_torque = 0.0
            
            # 计算从当前关节到末端的总重量和重心
            total_mass = 0.0
            total_moment = 0.0
            
            for j in range(i, self.num_joints):
                mass = self.link_masses[j]
                # 简化的重心位置计算
                if j == i:
                    moment_arm = self.link_coms[j] * math.cos(q[i])
                else:
                    moment_arm = self.link_lengths[j] * math.cos(sum(q[i:j+1]))
                
                total_mass += mass
                total_moment += mass * moment_arm
            
            gravity_torque = total_moment * self.gravity
            
            # 惯性力矩 (简化计算)
            inertia_torque = self.link_masses[i] * (self.link_lengths[i]**2) * qdd[i] / 3.0
            
            # 科里奥利和离心力 (简化)
            coriolis_torque = 0.1 * qd[i]**2 * math.sin(q[i])
            
            # 总力矩
            self.estimated_torques[i] = gravity_torque + inertia_torque + coriolis_torque
    
    def update_statistics(self):
        """更新统计数据"""
        for i in range(self.num_joints):
            # 位置范围
            self.max_positions[i] = max(self.max_positions[i], self.current_positions[i])
            self.min_positions[i] = min(self.min_positions[i], self.current_positions[i])
            
            # 最大值
            self.max_velocities[i] = max(self.max_velocities[i], abs(self.current_velocities[i]))
            self.max_accelerations[i] = max(self.max_accelerations[i], abs(self.current_accelerations[i]))
            self.max_torques[i] = max(self.max_torques[i], abs(self.estimated_torques[i]))
    
    def record_data(self, current_time):
        """记录数据"""
        relative_time = current_time - self.start_time
        data_point = {
            'time': relative_time,
            'positions': self.current_positions.copy(),
            'velocities': self.current_velocities.copy(),
            'accelerations': self.current_accelerations.copy(),
            'gazebo_efforts': self.current_efforts.copy(),
            'estimated_torques': self.estimated_torques.copy()
        }
        self.recorded_data.append(data_point)
    
    def print_current_status(self):
        """打印当前状态"""
        print("\n" + "="*120)
        print("PIPER机械臂实时关节状态 (Gazebo仿真 + 力矩估算)")
        print("="*120)
        print(f"运行时间: {rospy.get_time() - self.start_time:.2f}s | 数据点: {self.data_count} | 记录状态: {'开启' if self.recording else '关闭'}")
        print("-"*120)
        
        # 表头
        print(f"{'关节':<8} {'角度(rad)':<12} {'角度(°)':<10} {'角速度(rad/s)':<15} {'角加速度(rad/s²)':<18} {'Gazebo力矩':<12} {'估算力矩(N·m)':<15}")
        print("-"*120)
        
        # 数据行
        for i, joint_name in enumerate(self.joint_names):
            pos_rad = self.current_positions[i]
            pos_deg = math.degrees(pos_rad)
            vel = self.current_velocities[i]
            acc = self.current_accelerations[i]
            gazebo_eff = self.current_efforts[i]
            est_torque = self.estimated_torques[i]
            
            print(f"{joint_name:<8} {pos_rad:<12.4f} {pos_deg:<10.2f} {vel:<15.4f} {acc:<18.4f} {gazebo_eff:<12.4f} {est_torque:<15.4f}")
        
        print("="*120)
        
    def print_statistics(self):
        """打印统计信息"""
        print("\n" + "="*120)
        print("关节运动统计信息")
        print("="*120)
        
        print(f"{'关节':<8} {'角度范围(rad)':<20} {'角度范围(°)':<20} {'最大角速度':<15} {'最大角加速度':<18} {'最大估算力矩':<15}")
        print("-"*120)
        
        for i, joint_name in enumerate(self.joint_names):
            pos_range_rad = self.max_positions[i] - self.min_positions[i]
            pos_range_deg = math.degrees(pos_range_rad)
            min_deg = math.degrees(self.min_positions[i])
            max_deg = math.degrees(self.max_positions[i])
            
            print(f"{joint_name:<8} {pos_range_rad:<20.4f} {pos_range_deg:<20.2f} {self.max_velocities[i]:<15.4f} {self.max_accelerations[i]:<18.4f} {self.max_torques[i]:<15.4f}")
            print(f"{'':8} [{self.min_positions[i]:.3f}, {self.max_positions[i]:.3f}] [{min_deg:.1f}°, {max_deg:.1f}°]")
            print()
        
        print("="*120)
    
    def start_recording(self):
        """开始记录数据"""
        self.recording = True
        self.recorded_data = []
        self.start_time = rospy.get_time()
        rospy.loginfo("开始记录关节数据...")
        
    def stop_recording(self):
        """停止记录数据"""
        self.recording = False
        rospy.loginfo("停止记录，共记录 %d 个数据点", len(self.recorded_data))
        
    def save_data(self, filename=None):
        """保存记录的数据"""
        if not self.recorded_data:
            rospy.logwarn("没有数据可保存")
            return None
            
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"gazebo_joint_torque_data_{timestamp}.csv"
        
        # 创建保存目录
        save_dir = os.path.expanduser("~/piper_trajectory_data")
        if not os.path.exists(save_dir):
            os.makedirs(save_dir)
        
        filepath = os.path.join(save_dir, filename)
        
        # 保存CSV文件
        with open(filepath, 'w') as f:
            # 写入表头
            f.write('Time')
            for joint_name in self.joint_names:
                f.write(f',{joint_name}_pos,{joint_name}_vel,{joint_name}_acc,{joint_name}_gazebo_eff,{joint_name}_est_torque')
            f.write('\n')
            
            # 写入数据
            for data in self.recorded_data:
                f.write(f"{data['time']:.4f}")
                for i in range(self.num_joints):
                    f.write(f",{data['positions'][i]:.6f}")
                    f.write(f",{data['velocities'][i]:.6f}")
                    f.write(f",{data['accelerations'][i]:.6f}")
                    f.write(f",{data['gazebo_efforts'][i]:.6f}")
                    f.write(f",{data['estimated_torques'][i]:.6f}")
                f.write('\n')
        
        rospy.loginfo("数据已保存到: %s", filepath)
        return filepath
        
    def run_monitor(self, print_interval=2.0):
        """运行监控器"""
        rospy.loginfo("启动关节监控器...")
        rospy.loginfo("按 Ctrl+C 停止")
        
        try:
            rate = rospy.Rate(50)  # 50Hz更新频率
            last_print_time = time.time()
            
            while not rospy.is_shutdown():
                current_time = time.time()
                
                # 定期打印状态
                if current_time - last_print_time >= print_interval:
                    self.print_current_status()
                    last_print_time = current_time
                
                rate.sleep()
                
        except KeyboardInterrupt:
            rospy.loginfo("监控器被用户停止")
        except Exception as e:
            rospy.logerr("监控器失败: %s", str(e))
        finally:
            # 打印最终统计
            self.print_statistics()
            
            # 保存数据
            if self.recording:
                self.stop_recording()
                filepath = self.save_data()
                if filepath:
                    print(f"\n数据已保存到: {filepath}")

def main():
    """主函数"""
    try:
        monitor = JointMonitorWithTorque()
        
        # 交互式菜单
        print("\n=== Piper机械臂关节监控器(带力矩估算) ===")
        print("1. 仅监控显示")
        print("2. 监控并记录数据")
        
        choice = input("请选择模式 (1-2): ").strip()
        
        if choice == '2':
            monitor.start_recording()
            print("数据记录已开启")
        
        print("\n开始监控...")
        print("提示: 按 Ctrl+C 停止监控")
        
        # 设置打印间隔
        interval = input("输入状态打印间隔(秒，默认2): ").strip()
        try:
            interval = float(interval) if interval else 2.0
        except:
            interval = 2.0
        
        monitor.run_monitor(print_interval=interval)
        
    except KeyboardInterrupt:
        rospy.loginfo("程序被用户中断")
    except Exception as e:
        rospy.logerr("程序失败: %s", str(e))

if __name__ == '__main__':
    main()
