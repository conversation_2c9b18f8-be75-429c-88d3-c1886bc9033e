#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# Copyright 2019 Wuhan PS-Micro Technology Co., Itd.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import rospy, sys
import moveit_commander
from moveit_commander import MoveGroupCommander
from geometry_msgs.msg import Pose, PoseStamped
from copy import deepcopy
import moveit_msgs.msg

import math
import numpy

class MoveItCircleDemo:
    def __init__(self):
        # 初始化move_group的API
        moveit_commander.roscpp_initialize(sys.argv)

        # 初始化ROS节点
        rospy.init_node('moveit_clrcle_demo', anonymous=True)
                        
        # 初始化需要使用move group控制的机械臂中的arm group
        arm = MoveGroupCommander('arm')
        
        # 当运动规划失败后，允许重新规划
        arm.allow_replanning(True)
        
        # 设置目标位置所使用的参考坐标系
        reference_frame = 'base_link'
        arm.set_pose_reference_frame('base_link')
                
        # 设置位置(单位：米)和姿态（单位：弧度）的允许误差
        arm.set_goal_position_tolerance(0.01)
        arm.set_goal_orientation_tolerance(0.01)
        
        # 设置允许的最大速度和加速度
        arm.set_max_acceleration_scaling_factor(0.5)
        arm.set_max_velocity_scaling_factor(0.5)
        
        # 获取终端link的名称
        end_effector_link = arm.get_end_effector_link()

        # 获取关节限制信息
        joint_names = arm.get_joints()
        rospy.loginfo("Joint names: %s", joint_names)
        
        # 获取当前关节值
        current_joints = arm.get_current_joint_values()
        rospy.loginfo("Current joint values: %s", current_joints)
        
        # 设置安全的小幅度运动目标(当前值±10%)
        joint_target = [j * 1.1 for j in current_joints[:6]]  # 只处理前6个关节
        
        # 检查目标是否在限制范围内
        try:
            arm.set_joint_value_target(joint_target)
        except Exception as e:
            rospy.logerr("Invalid joint target: %s", str(e))
            joint_target = current_joints  # 回退到当前位置
            rospy.logwarn("Using current position as fallback target")
        
        # 设置规划参数
        arm.set_planning_time(10)
        arm.set_max_velocity_scaling_factor(0.1)  # 降低速度
        
        # 首先移动到一个安全的起始位置
        try:
            arm.set_named_target('zero')
            arm.go()
            rospy.sleep(1)
        except Exception as e:
            rospy.logwarn("Could not move to named target 'zero': %s", str(e))
            rospy.loginfo("Continuing with current position")

        # 实现圆弧轨迹规划
        waypoints = []
        start_pose = arm.get_current_pose().pose

        rospy.loginfo("Start pose: x=%.3f, y=%.3f, z=%.3f",
                     start_pose.position.x, start_pose.position.y, start_pose.position.z)

        # 创建一个更简单的路径，减小移动距离
        waypoints.append(deepcopy(start_pose))  # 起始点

        # 第一个路径点：小幅向前移动
        wpose1 = deepcopy(start_pose)
        wpose1.position.x += 0.02  # 减小移动距离
        waypoints.append(wpose1)

        # 第二个路径点：小幅向上移动
        wpose2 = deepcopy(wpose1)
        wpose2.position.z += 0.02  # 减小移动距离
        waypoints.append(wpose2)

        # 第三个路径点：小幅向右移动
        wpose3 = deepcopy(wpose2)
        wpose3.position.y += 0.02  # 减小移动距离
        waypoints.append(wpose3)
        
        # 规划笛卡尔路径
        try:
            # 转换waypoints为MoveIt接受的格式
            moveit_waypoints = []
            for pose in waypoints:
                moveit_waypoints.append(pose)
            
            # 按照正确的Python API调用 (只需要waypoints和eef_step)
            (plan, fraction) = arm.compute_cartesian_path(
                moveit_waypoints,  # 路径点列表
                0.01)             # eef_step (米)
            
            rospy.loginfo("Planning with %d waypoints", len(waypoints))

            if fraction > 0.0:
                rospy.loginfo("Path planning succeeded (%.2f%%)", fraction*100)

                # 显示规划轨迹
                display_trajectory = moveit_msgs.msg.DisplayTrajectory()
                display_trajectory.trajectory_start = arm.get_current_state()
                display_trajectory.trajectory.append(plan)
                display_pub = rospy.Publisher('/move_group/display_planned_path',
                                             moveit_msgs.msg.DisplayTrajectory,
                                             queue_size=20)
                display_pub.publish(display_trajectory)

                # 执行规划的轨迹
                rospy.loginfo("Executing planned trajectory...")
                arm.execute(plan, wait=True)
                rospy.loginfo("Trajectory execution completed!")
            else:
                rospy.logwarn("Path planning failed completely (%.2f%%)", fraction*100)
                
        except Exception as e:
            rospy.logerr("Failed to compute cartesian path: %s", str(e))
        
        # 关闭并退出moveit
        moveit_commander.roscpp_shutdown()
        moveit_commander.os._exit(0)

if __name__ == "__main__":
    try:
        MoveItCircleDemo()
    except rospy.ROSInterruptException:
        pass
