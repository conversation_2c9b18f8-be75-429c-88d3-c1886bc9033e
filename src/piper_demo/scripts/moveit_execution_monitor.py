#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
MoveIt执行监控器
功能：
1. 监控MoveIt的plan和execute操作
2. 自动记录执行过程中的关节数据
3. 执行完成后自动生成图表
4. 解决中文乱码问题
"""

import rospy
import sys
import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from sensor_msgs.msg import JointState
from moveit_msgs.msg import ExecuteTrajectoryActionGoal, ExecuteTrajectoryActionResult
import math
import time
from datetime import datetime
import os
import csv

class MoveItExecutionMonitor:
    def __init__(self):
        """初始化MoveIt执行监控器"""
        rospy.init_node('moveit_execution_monitor', anonymous=True)
        
        # 设置matplotlib中文字体
        self.setup_matplotlib()
        
        # 数据存储
        self.joint_data = {'Time': []}
        self.joint_names = ['Joint1', 'Joint2', 'Joint3', 'Joint4', 'Joint5', 'Joint6']
        for name in self.joint_names:
            self.joint_data[name] = []
        
        self.pose_data = {'Time': [], 'X': [], 'Y': [], 'Z': [], 'Qx': [], 'Qy': [], 'Qz': [], 'Qw': []}
        
        # 监控状态
        self.is_recording = False
        self.execution_start_time = None
        self.execution_count = 0
        
        # 数据保存目录
        self.data_dir = os.path.expanduser("~/piper_trajectory_data")
        if not os.path.exists(self.data_dir):
            os.makedirs(self.data_dir)
        
        # 订阅MoveIt执行状态
        self.execute_goal_sub = rospy.Subscriber(
            '/execute_trajectory/goal', 
            ExecuteTrajectoryActionGoal, 
            self.execution_start_callback
        )
        
        self.execute_result_sub = rospy.Subscriber(
            '/execute_trajectory/result', 
            ExecuteTrajectoryActionResult, 
            self.execution_end_callback
        )
        
        # 订阅关节状态
        self.joint_sub = rospy.Subscriber('/joint_states', JointState, self.joint_state_callback)
        
        print("=" * 60)
        print("🤖 MoveIt Execution Monitor Started")
        print("=" * 60)
        print("📋 Instructions:")
        print("1. Open MoveIt in RViz")
        print("2. Plan your trajectory")
        print("3. Click Execute - monitoring will start automatically")
        print("4. Charts will be generated after execution completes")
        print("5. Press Ctrl+C to stop monitoring")
        print("-" * 60)
        print(f"📁 Data will be saved to: {self.data_dir}")
        print("=" * 60)
        
    def setup_matplotlib(self):
        """设置matplotlib避免中文乱码"""
        # 设置字体为支持中英文的字体
        plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Arial', 'SimHei', 'Microsoft YaHei']
        plt.rcParams['axes.unicode_minus'] = False
        plt.rcParams['figure.figsize'] = (12, 8)
        plt.rcParams['font.size'] = 11
        plt.rcParams['axes.grid'] = True
        plt.rcParams['grid.alpha'] = 0.3
        plt.rcParams['lines.linewidth'] = 2
        
        # 使用英文标签避免乱码
        self.labels = {
            'title_joint_angles': 'Piper Robot Joint Angles During Execution',
            'title_3d_trajectory': 'End-Effector 3D Trajectory',
            'title_yz_trajectory': 'End-Effector Trajectory Analysis',
            'title_velocities': 'Joint Angular Velocities',
            'xlabel_time': 'Time (s)',
            'ylabel_angle': 'Angle (degrees)',
            'ylabel_velocity': 'Angular Velocity (deg/s)',
            'xlabel_y': 'Y Position (m)',
            'ylabel_z': 'Z Position (m)',
            'xlabel_x': 'X Position (m)',
            'ylabel_y': 'Y Position (m)',
            'zlabel_z': 'Z Position (m)',
            'label_start': 'Start',
            'label_end': 'End',
            'label_trajectory': 'Trajectory',
            'label_actual': 'Actual Path',
            'label_ideal': 'Ideal Circle',
            'text_range': 'Range',
            'text_min': 'Min',
            'text_max': 'Max',
            'text_rms': 'RMS',
            'text_error': 'Trajectory Error (mm)'
        }
        
    def execution_start_callback(self, msg):
        """MoveIt开始执行回调"""
        if not self.is_recording:
            self.execution_count += 1
            print(f"\n🚀 Execution #{self.execution_count} started - Recording data...")
            self.start_recording()
            
    def execution_end_callback(self, msg):
        """MoveIt执行完成回调"""
        if self.is_recording:
            print(f"✅ Execution #{self.execution_count} completed - Generating charts...")
            self.stop_recording()
            self.generate_all_charts()
            print(f"📊 Charts generated for execution #{self.execution_count}")
            print("-" * 60)
            print("Ready for next execution...")
            
    def start_recording(self):
        """开始记录数据"""
        self.is_recording = True
        self.execution_start_time = rospy.get_time()
        
        # 清空数据
        self.joint_data = {'Time': []}
        for name in self.joint_names:
            self.joint_data[name] = []
        self.pose_data = {'Time': [], 'X': [], 'Y': [], 'Z': [], 'Qx': [], 'Qy': [], 'Qz': [], 'Qw': []}
        
    def stop_recording(self):
        """停止记录数据"""
        self.is_recording = False
        print(f"📈 Recorded {len(self.joint_data['Time'])} data points")
        
    def joint_state_callback(self, msg):
        """关节状态回调"""
        if not self.is_recording:
            return
            
        current_time = rospy.get_time() - self.execution_start_time
        self.joint_data['Time'].append(current_time)
        
        # 记录关节角度
        joint_names_ros = ['joint1', 'joint2', 'joint3', 'joint4', 'joint5', 'joint6']
        for i, joint_name in enumerate(joint_names_ros):
            if joint_name in msg.name:
                idx = msg.name.index(joint_name)
                self.joint_data[self.joint_names[i]].append(msg.position[idx])
            else:
                self.joint_data[self.joint_names[i]].append(0.0)
        
        # 记录末端执行器位置（简化计算）
        # 这里使用简单的正运动学估算
        try:
            # 简化的末端位置计算
            q1, q2, q3 = msg.position[0], msg.position[1], msg.position[2]
            
            # 简化的DH参数计算
            L1, L2, L3 = 0.1273, 0.105, 0.098
            
            x = (L2 * math.cos(q2) + L3 * math.cos(q2 + q3)) * math.cos(q1)
            y = (L2 * math.cos(q2) + L3 * math.cos(q2 + q3)) * math.sin(q1)
            z = L1 + L2 * math.sin(q2) + L3 * math.sin(q2 + q3)
            
            self.pose_data['Time'].append(current_time)
            self.pose_data['X'].append(x)
            self.pose_data['Y'].append(y)
            self.pose_data['Z'].append(z)
            self.pose_data['Qx'].append(0.0)  # 简化
            self.pose_data['Qy'].append(0.0)
            self.pose_data['Qz'].append(0.0)
            self.pose_data['Qw'].append(1.0)
            
        except:
            # 如果计算失败，使用零值
            self.pose_data['Time'].append(current_time)
            for key in ['X', 'Y', 'Z', 'Qx', 'Qy', 'Qz']:
                self.pose_data[key].append(0.0)
            self.pose_data['Qw'].append(1.0)
    
    def save_data(self):
        """保存数据到CSV"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 保存关节数据
        joint_file = os.path.join(self.data_dir, f'moveit_execution_{self.execution_count}_{timestamp}.csv')
        with open(joint_file, 'w') as f:
            f.write('Time,Joint1,Joint2,Joint3,Joint4,Joint5,Joint6\n')
            for i in range(len(self.joint_data['Time'])):
                f.write(f"{self.joint_data['Time'][i]:.4f}")
                for name in self.joint_names:
                    f.write(f",{self.joint_data[name][i]:.6f}")
                f.write('\n')
        
        print(f"💾 Data saved: {os.path.basename(joint_file)}")
        return timestamp
        
    def generate_joint_angles_chart(self, timestamp):
        """生成关节角度图表"""
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle(self.labels['title_joint_angles'], fontsize=16, fontweight='bold')
        
        colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#FFA07A', '#98D8C8', '#F7DC6F']
        time_data = self.joint_data['Time']
        
        for i, (joint_name, color) in enumerate(zip(self.joint_names, colors)):
            row, col = i // 3, i % 3
            ax = axes[row, col]
            
            angles_deg = np.degrees(self.joint_data[joint_name])
            ax.plot(time_data, angles_deg, color=color, linewidth=2.5)
            
            ax.set_title(f'{joint_name} Angle', fontsize=14, fontweight='bold')
            ax.set_xlabel(self.labels['xlabel_time'], fontsize=12)
            ax.set_ylabel(self.labels['ylabel_angle'], fontsize=12)
            ax.grid(True, alpha=0.3)
            
            # 统计信息
            min_angle = np.min(angles_deg)
            max_angle = np.max(angles_deg)
            range_angle = max_angle - min_angle
            stats_text = f'{self.labels["text_range"]}: {range_angle:.1f}°\n{self.labels["text_min"]}: {min_angle:.1f}°\n{self.labels["text_max"]}: {max_angle:.1f}°'
            ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, verticalalignment='top',
                   bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.8))
        
        plt.tight_layout()
        save_path = os.path.join(self.data_dir, f'joint_angles_exec{self.execution_count}_{timestamp}.png')
        plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
        plt.close()
        return save_path
        
    def generate_3d_trajectory_chart(self, timestamp):
        """生成3D轨迹图表"""
        if len(self.pose_data['X']) < 2:
            return None
            
        fig = plt.figure(figsize=(12, 10))
        ax = fig.add_subplot(111, projection='3d')
        
        x_data = self.pose_data['X']
        y_data = self.pose_data['Y']
        z_data = self.pose_data['Z']
        
        ax.plot(x_data, y_data, z_data, 'b-', linewidth=3, label=self.labels['label_trajectory'], alpha=0.8)
        ax.scatter(x_data[0], y_data[0], z_data[0], color='green', s=200, label=self.labels['label_start'], marker='o')
        ax.scatter(x_data[-1], y_data[-1], z_data[-1], color='red', s=200, label=self.labels['label_end'], marker='s')
        
        ax.set_xlabel(self.labels['xlabel_x'], fontsize=12, fontweight='bold')
        ax.set_ylabel(self.labels['ylabel_y'], fontsize=12, fontweight='bold')
        ax.set_zlabel(self.labels['zlabel_z'], fontsize=12, fontweight='bold')
        ax.set_title(self.labels['title_3d_trajectory'], fontsize=16, fontweight='bold')
        ax.legend(fontsize=12)
        
        save_path = os.path.join(self.data_dir, f'3d_trajectory_exec{self.execution_count}_{timestamp}.png')
        plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
        plt.close()
        return save_path

    def generate_velocity_chart(self, timestamp):
        """生成关节角速度图表"""
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle(self.labels['title_velocities'], fontsize=16, fontweight='bold')

        colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#FFA07A', '#98D8C8', '#F7DC6F']
        time_data = np.array(self.joint_data['Time'])

        for i, (joint_name, color) in enumerate(zip(self.joint_names, colors)):
            row, col = i // 3, i % 3
            ax = axes[row, col]

            # 计算角速度
            angles = np.array(self.joint_data[joint_name])
            velocities = np.gradient(angles, time_data)
            velocities_deg = np.degrees(velocities)

            ax.plot(time_data, velocities_deg, color=color, linewidth=2.5)
            ax.set_title(f'{joint_name} Angular Velocity', fontsize=14, fontweight='bold')
            ax.set_xlabel(self.labels['xlabel_time'], fontsize=12)
            ax.set_ylabel(self.labels['ylabel_velocity'], fontsize=12)
            ax.grid(True, alpha=0.3)

            # 统计信息
            max_vel = np.max(np.abs(velocities_deg))
            rms_vel = np.sqrt(np.mean(velocities_deg**2))
            stats_text = f'{self.labels["text_max"]}: {max_vel:.1f}°/s\n{self.labels["text_rms"]}: {rms_vel:.1f}°/s'
            ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, verticalalignment='top',
                   bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.8))

        plt.tight_layout()
        save_path = os.path.join(self.data_dir, f'joint_velocities_exec{self.execution_count}_{timestamp}.png')
        plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
        plt.close()
        return save_path

    def generate_yz_trajectory_chart(self, timestamp):
        """生成Y-Z轨迹图表"""
        if len(self.pose_data['Y']) < 2:
            return None

        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))
        fig.suptitle(self.labels['title_yz_trajectory'], fontsize=16, fontweight='bold')

        y_data = np.array(self.pose_data['Y'])
        z_data = np.array(self.pose_data['Z'])
        time_data = self.pose_data['Time']

        # Y-Z轨迹
        ax1.plot(y_data, z_data, 'b-', linewidth=3, label=self.labels['label_actual'], alpha=0.8)
        ax1.scatter(y_data[0], z_data[0], color='green', s=150, label=self.labels['label_start'], marker='o')
        ax1.scatter(y_data[-1], z_data[-1], color='red', s=150, label=self.labels['label_end'], marker='s')

        ax1.set_xlabel(self.labels['xlabel_y'], fontsize=12, fontweight='bold')
        ax1.set_ylabel(self.labels['ylabel_z'], fontsize=12, fontweight='bold')
        ax1.set_title('Y-Z Plane Trajectory', fontsize=14, fontweight='bold')
        ax1.legend(fontsize=12)
        ax1.grid(True, alpha=0.3)
        ax1.axis('equal')

        # 轨迹分析
        if len(y_data) > 10:  # 只有足够数据点才分析
            # 计算轨迹长度
            distances = []
            for i in range(1, len(y_data)):
                dy = y_data[i] - y_data[i-1]
                dz = z_data[i] - z_data[i-1]
                dist = np.sqrt(dy**2 + dz**2)
                distances.append(dist)

            cumulative_dist = np.cumsum([0] + distances)
            ax2.plot(time_data, cumulative_dist, 'g-', linewidth=2, label='Cumulative Distance')
            ax2.set_xlabel(self.labels['xlabel_time'], fontsize=12, fontweight='bold')
            ax2.set_ylabel('Distance (m)', fontsize=12, fontweight='bold')
            ax2.set_title('Trajectory Distance vs Time', fontsize=14, fontweight='bold')
            ax2.grid(True, alpha=0.3)

            total_dist = cumulative_dist[-1]
            avg_speed = total_dist / time_data[-1] if time_data[-1] > 0 else 0
            stats_text = f'Total: {total_dist:.3f}m\nAvg Speed: {avg_speed:.3f}m/s'
            ax2.text(0.02, 0.98, stats_text, transform=ax2.transAxes, verticalalignment='top',
                    bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.8))

        plt.tight_layout()
        save_path = os.path.join(self.data_dir, f'yz_trajectory_exec{self.execution_count}_{timestamp}.png')
        plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
        plt.close()
        return save_path

    def generate_all_charts(self):
        """生成所有图表"""
        if len(self.joint_data['Time']) < 2:
            print("❌ Not enough data to generate charts")
            return

        # 保存数据
        timestamp = self.save_data()

        print("📊 Generating charts...")
        saved_files = []

        try:
            # 1. 关节角度图表
            path1 = self.generate_joint_angles_chart(timestamp)
            if path1:
                saved_files.append(path1)
                print(f"✅ Joint angles chart: {os.path.basename(path1)}")

            # 2. 3D轨迹图表
            path2 = self.generate_3d_trajectory_chart(timestamp)
            if path2:
                saved_files.append(path2)
                print(f"✅ 3D trajectory chart: {os.path.basename(path2)}")

            # 3. 角速度图表
            path3 = self.generate_velocity_chart(timestamp)
            if path3:
                saved_files.append(path3)
                print(f"✅ Velocity chart: {os.path.basename(path3)}")

            # 4. Y-Z轨迹图表
            path4 = self.generate_yz_trajectory_chart(timestamp)
            if path4:
                saved_files.append(path4)
                print(f"✅ Y-Z trajectory chart: {os.path.basename(path4)}")

            print(f"🎉 Generated {len(saved_files)} charts for execution #{self.execution_count}")

        except Exception as e:
            print(f"❌ Error generating charts: {e}")

    def run_monitor(self):
        """运行监控器"""
        try:
            rospy.spin()
        except KeyboardInterrupt:
            print("\n👋 Monitor stopped by user")
        except Exception as e:
            print(f"❌ Monitor error: {e}")

def main():
    """主函数"""
    try:
        monitor = MoveItExecutionMonitor()
        monitor.run_monitor()
    except KeyboardInterrupt:
        print("\n👋 Program interrupted by user")
    except Exception as e:
        print(f"❌ Program error: {e}")

if __name__ == '__main__':
    main()
