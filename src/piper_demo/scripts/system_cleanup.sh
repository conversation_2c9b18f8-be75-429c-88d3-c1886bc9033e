#!/bin/bash

# Piper机械臂系统清理和优化脚本
# 解决终端变慢问题

echo "=== Piper机械臂系统清理工具 ==="
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 函数：打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 1. 检查系统状态
check_system_status() {
    print_info "检查系统状态..."
    
    # 检查内存使用
    echo "内存使用情况:"
    free -h
    echo ""
    
    # 检查CPU使用
    echo "CPU使用情况:"
    top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1
    echo ""
    
    # 检查ROS进程
    echo "ROS进程数量:"
    ps aux | grep ros | grep -v grep | wc -l
    echo ""
    
    # 检查Python进程
    echo "Python进程数量:"
    ps aux | grep python | grep -v grep | wc -l
    echo ""
}

# 2. 清理ROS进程
cleanup_ros_processes() {
    print_info "清理ROS进程..."
    
    # 检查是否有ROS Master运行
    if pgrep -f "rosmaster" > /dev/null; then
        print_warning "发现ROS Master正在运行"
        read -p "是否要停止所有ROS进程? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            print_info "停止ROS进程..."
            
            # 优雅地停止ROS节点
            rosnode kill -a 2>/dev/null || true
            sleep 2
            
            # 强制停止ROS相关进程
            pkill -f "ros" 2>/dev/null || true
            pkill -f "rviz" 2>/dev/null || true
            pkill -f "gazebo" 2>/dev/null || true
            pkill -f "moveit" 2>/dev/null || true
            
            sleep 2
            print_success "ROS进程已清理"
        else
            print_info "跳过ROS进程清理"
        fi
    else
        print_info "没有发现运行中的ROS Master"
    fi
}

# 3. 清理Python进程
cleanup_python_processes() {
    print_info "清理Python进程..."
    
    # 查找可能的僵尸Python进程
    python_procs=$(ps aux | grep python | grep -E "(matplotlib|piper_demo)" | grep -v grep | awk '{print $2}')
    
    if [ ! -z "$python_procs" ]; then
        print_warning "发现相关Python进程:"
        ps aux | grep python | grep -E "(matplotlib|piper_demo)" | grep -v grep
        echo ""
        
        read -p "是否要停止这些Python进程? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            echo "$python_procs" | xargs kill -TERM 2>/dev/null || true
            sleep 2
            echo "$python_procs" | xargs kill -KILL 2>/dev/null || true
            print_success "Python进程已清理"
        fi
    else
        print_info "没有发现需要清理的Python进程"
    fi
}

# 4. 清理日志文件
cleanup_logs() {
    print_info "清理日志文件..."
    
    # ROS日志目录
    ros_log_dir="$HOME/.ros/log"
    if [ -d "$ros_log_dir" ]; then
        log_size=$(du -sh "$ros_log_dir" 2>/dev/null | cut -f1)
        print_info "ROS日志目录大小: $log_size"
        
        if [ "$log_size" != "0" ]; then
            read -p "是否要清理ROS日志文件? (y/N): " -n 1 -r
            echo
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                # 保留最近的日志，删除7天前的
                find "$ros_log_dir" -type f -mtime +7 -delete 2>/dev/null || true
                print_success "旧的ROS日志文件已清理"
            fi
        fi
    fi
    
    # 清理临时文件
    print_info "清理临时文件..."
    rm -rf /tmp/ros_* 2>/dev/null || true
    rm -rf /tmp/matplotlib* 2>/dev/null || true
    print_success "临时文件已清理"
}

# 5. 清理图形缓存
cleanup_graphics() {
    print_info "清理图形缓存..."
    
    # 清理matplotlib缓存
    if [ -d "$HOME/.cache/matplotlib" ]; then
        rm -rf "$HOME/.cache/matplotlib" 2>/dev/null || true
        print_success "matplotlib缓存已清理"
    fi
    
    # 清理X11相关
    if [ ! -z "$DISPLAY" ]; then
        # 关闭可能的图形窗口
        pkill -f "python.*matplotlib" 2>/dev/null || true
        print_success "图形进程已清理"
    fi
}

# 6. 优化系统设置
optimize_system() {
    print_info "优化系统设置..."
    
    # 清理内存缓存
    if [ -w /proc/sys/vm/drop_caches ]; then
        sync
        echo 3 | sudo tee /proc/sys/vm/drop_caches > /dev/null 2>&1 || true
        print_success "内存缓存已清理"
    else
        print_warning "无法清理内存缓存 (需要sudo权限)"
    fi
    
    # 设置环境变量优化
    export PYTHONDONTWRITEBYTECODE=1
    export MPLBACKEND=Agg  # 使用非交互式后端
    
    print_success "环境变量已优化"
}

# 7. 检查磁盘空间
check_disk_space() {
    print_info "检查磁盘空间..."
    
    echo "磁盘使用情况:"
    df -h | grep -E "(Filesystem|/dev/)"
    echo ""
    
    # 检查工作空间大小
    workspace_dir="/home/<USER>/piper_ros"
    if [ -d "$workspace_dir" ]; then
        workspace_size=$(du -sh "$workspace_dir" 2>/dev/null | cut -f1)
        print_info "工作空间大小: $workspace_size"
    fi
    
    # 检查数据目录大小
    data_dir="$HOME/piper_trajectory_data"
    if [ -d "$data_dir" ]; then
        data_size=$(du -sh "$data_dir" 2>/dev/null | cut -f1)
        print_info "轨迹数据目录大小: $data_size"
        
        # 如果数据目录过大，提示清理
        data_size_mb=$(du -sm "$data_dir" 2>/dev/null | cut -f1)
        if [ "$data_size_mb" -gt 100 ]; then
            print_warning "轨迹数据目录较大 (${data_size})"
            read -p "是否要清理旧的轨迹数据? (y/N): " -n 1 -r
            echo
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                # 保留最近的5个数据文件
                cd "$data_dir"
                ls -t *.csv 2>/dev/null | tail -n +11 | xargs rm -f 2>/dev/null || true
                ls -t *.png 2>/dev/null | tail -n +16 | xargs rm -f 2>/dev/null || true
                print_success "旧的轨迹数据已清理"
            fi
        fi
    fi
}

# 8. 创建优化的启动脚本
create_optimized_launcher() {
    print_info "创建优化的启动脚本..."
    
    cat > "/tmp/optimized_piper_demo.sh" << 'EOF'
#!/bin/bash
# 优化的Piper演示启动脚本

# 设置环境变量
export PYTHONDONTWRITEBYTECODE=1
export MPLBACKEND=Qt5Agg
export ROS_PYTHON_LOG_CONFIG_FILE=""

# 限制Python内存使用
ulimit -v 2097152  # 2GB虚拟内存限制

# 启动演示
cd /home/<USER>/piper_ros
source devel/setup.bash
rosrun piper_demo circle_demo_with_plot.py
EOF
    
    chmod +x "/tmp/optimized_piper_demo.sh"
    print_success "优化启动脚本已创建: /tmp/optimized_piper_demo.sh"
}

# 主函数
main() {
    echo "开始系统清理和优化..."
    echo ""
    
    # 检查系统状态
    check_system_status
    
    # 执行清理操作
    cleanup_ros_processes
    cleanup_python_processes
    cleanup_logs
    cleanup_graphics
    optimize_system
    check_disk_space
    create_optimized_launcher
    
    echo ""
    print_success "系统清理和优化完成!"
    echo ""
    
    # 显示优化后的状态
    print_info "优化后的系统状态:"
    echo "内存使用:"
    free -h | grep "Mem:"
    echo ""
    
    print_info "建议的后续操作:"
    echo "1. 重新打开终端以获得最佳性能"
    echo "2. 使用优化的启动脚本: /tmp/optimized_piper_demo.sh"
    echo "3. 定期运行此清理脚本"
    echo ""
}

# 运行主函数
main
