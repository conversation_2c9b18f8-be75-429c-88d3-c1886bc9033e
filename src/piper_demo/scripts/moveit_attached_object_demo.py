#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# Copyright 2019 Wuhan PS-Micro Technology Co., Itd.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import sys
import rospy
import moveit_commander
from moveit_commander import MoveGroupCommander, PlanningSceneInterface
from geometry_msgs.msg import PoseStamped
from moveit_msgs.msg import AttachedCollisionObject

class MoveAttachedObjectDemo:
    def __init__(self):
        # 初始化move_group的API
        moveit_commander.roscpp_initialize(sys.argv)

        try:
            # 初始化ROS节点
            rospy.init_node('moveit_attached_object_demo')

            # 初始化场景对象
            scene = PlanningSceneInterface()
            rospy.sleep(1)

            # 初始化需要使用move group控制的机械臂中的arm group
            piper = MoveGroupCommander('piper')

            # 获取终端link的名称 (link7)
            end_effector_link = 'link8'

            # 设置位置(单位：米)和姿态（单位：弧度）的允许误差
            piper.set_goal_position_tolerance(0.01)
            piper.set_goal_orientation_tolerance(0.05)

            # 当运动规划失败后，允许重新规划
            piper.allow_replanning(True)

            # 设置每次运动规划的时间限制：10s
            piper.set_planning_time(10)

            # 移除场景中之前运行残留的物体
            scene.remove_attached_object(end_effector_link, 'tool')
            scene.remove_world_object('table')
            scene.remove_world_object('target')

            # 设置更保守的工具尺寸和位置
            table_size = [0.4, 0.6, 0.02]  # 更大的table避免边缘碰撞
            tool_size = [0.1, 0.01, 0.01]  # 最小化的工具尺寸

            # 设置工具位姿 - 更靠近末端
            tool_pose = PoseStamped()
            tool_pose.header.frame_id = end_effector_link
            tool_pose.pose.position.x = 0.05  # 减少延伸
            tool_pose.pose.position.y = 0.0
            tool_pose.pose.position.z = 0.0
            tool_pose.pose.orientation.w = 1.0

            # 添加工具前先排除与机械臂的碰撞
            scene.remove_attached_object(end_effector_link, 'tool')
            rospy.sleep(0.5)
            scene.attach_box(end_effector_link, 'tool', tool_pose, tool_size)

            # 设置更保守的table位置
            table_pose = PoseStamped()
            table_pose.header.frame_id = 'base_link'
            table_pose.pose.position.x = 0.3  # 稍远位置
            table_pose.pose.position.y = 0.2   # 更大侧向偏移
            table_pose.pose.position.z = 0.6   # 适度高度
            table_pose.pose.orientation.w = 1.0
            
            # 先清除旧table再添加新table
            scene.remove_world_object('table')
            rospy.sleep(0.5)
            scene.add_box('table', table_pose, table_size)

            # 确保场景更新完成
            rospy.sleep(2)

            # 强化避障规划设置
            piper.set_workspace([-0.5, -0.5, 0], [0.5, 0.5, 1])  # 更精确的工作空间
            piper.set_support_surface_name("table")
            piper.set_planner_id("RRTConnect")  # 指定更可靠的规划器

            # 设置绝对安全的初始关节位置
            joint_positions = [0, 0.5, -0.5, 0, 0, 0]  # 更保守的初始位置
            
            # 确保机械臂从零位开始
            piper.set_named_target('zero')
            piper.go(wait=True)
            rospy.sleep(1)

            # 确保工具正确连接到末端执行器
            tool_pose = PoseStamped()
            tool_pose.header.frame_id = end_effector_link
            tool_pose.pose.position.x = 0.1  # 调整工具位置
            tool_pose.pose.position.y = 0.0
            tool_pose.pose.position.z = 0.0
            tool_pose.pose.orientation.w = 1.0
            piper.set_joint_value_target(joint_positions)

            # 控制机械臂完成运动
            if piper.go(wait=True):
                rospy.loginfo("机械臂成功移动到目标位置")
            else:
                rospy.logerr("机械臂移动到目标位置失败")

            rospy.sleep(1)

            # 控制机械臂回到初始化位置
            piper.set_named_target('zero')
            if piper.go(wait=True):
                rospy.loginfo("机械臂成功回到初始化位置")
            else:
                rospy.logerr("机械臂回到初始化位置失败")

        except rospy.ROSInterruptException:
            rospy.logerr("ROS中断，程序提前退出")
        except Exception as e:
            rospy.logerr(f"发生错误: {e}")

        finally:
            # 停止move_group的API
            moveit_commander.roscpp_shutdown()

if __name__ == "__main__":
    MoveAttachedObjectDemo()
