#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Piper机械臂图表生成器
功能：
1. 一键生成所有类型的图表
2. 支持实时数据采集和图表生成
3. 自动保存高质量图片
4. 专门用于论文和报告
"""

import rospy
import sys
import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from sensor_msgs.msg import JointState
import math
import time
from datetime import datetime
import os
import csv
import glob

class ChartGenerator:
    def __init__(self):
        """初始化图表生成器"""
        self.data_dir = os.path.expanduser("~/piper_trajectory_data")
        
        # 确保数据目录存在
        if not os.path.exists(self.data_dir):
            os.makedirs(self.data_dir)
            
        # 设置matplotlib参数和中文字体
        self.setup_chinese_font()
        plt.rcParams['figure.figsize'] = (12, 8)
        plt.rcParams['font.size'] = 12
        plt.rcParams['axes.grid'] = True
        plt.rcParams['grid.alpha'] = 0.3
        plt.rcParams['lines.linewidth'] = 2
        
        print("=== Piper Robot Chart Generator ===")
        print(f"Data directory: {self.data_dir}")

    def setup_chinese_font(self):
        """设置中文字体支持"""
        try:
            # 尝试设置中文字体
            import matplotlib.font_manager as fm

            # 常见的中文字体路径
            chinese_fonts = [
                '/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf',
                '/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf',
                '/System/Library/Fonts/Arial.ttf',  # macOS
                'C:/Windows/Fonts/simhei.ttf',      # Windows
                'SimHei', 'Microsoft YaHei', 'DejaVu Sans'
            ]

            font_found = False
            for font in chinese_fonts:
                try:
                    if font.endswith('.ttf'):
                        if os.path.exists(font):
                            plt.rcParams['font.sans-serif'] = [font]
                            font_found = True
                            break
                    else:
                        plt.rcParams['font.sans-serif'] = [font]
                        font_found = True
                        break
                except:
                    continue

            if not font_found:
                # 使用英文标签避免乱码
                print("Warning: Chinese font not found, using English labels")
                self.use_english = True
            else:
                self.use_english = False

            plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

        except Exception as e:
            print(f"Font setup warning: {e}")
            self.use_english = True
        
    def load_csv_data(self, filename):
        """加载CSV数据"""
        data = {}
        with open(filename, 'r') as f:
            reader = csv.DictReader(f)
            for key in reader.fieldnames:
                data[key] = []
            
            for row in reader:
                for key, value in row.items():
                    try:
                        data[key].append(float(value))
                    except ValueError:
                        data[key].append(value)
        return data
        
    def get_latest_data(self):
        """获取最新数据文件"""
        joint_files = glob.glob(os.path.join(self.data_dir, "joint_data_*.csv"))
        pose_files = glob.glob(os.path.join(self.data_dir, "pose_data_*.csv"))
        
        if not joint_files or not pose_files:
            print("❌ 没有找到数据文件!")
            print("请先运行轨迹演示程序生成数据:")
            print("  rosrun piper_demo circle_demo_with_plot.py")
            return None, None
            
        latest_joint = max(joint_files, key=os.path.getctime)
        latest_pose = max(pose_files, key=os.path.getctime)
        
        print(f"📁 加载数据文件:")
        print(f"  关节数据: {os.path.basename(latest_joint)}")
        print(f"  位姿数据: {os.path.basename(latest_pose)}")
        
        joint_data = self.load_csv_data(latest_joint)
        pose_data = self.load_csv_data(latest_pose)
        
        print(f"  数据点数: {len(joint_data['Time'])}")
        print(f"  时间范围: {min(joint_data['Time']):.2f}s - {max(joint_data['Time']):.2f}s")
        
        return joint_data, pose_data
        
    def generate_joint_angles_chart(self, joint_data, save_path=None):
        """生成关节角度图表"""
        print("📊 Generating joint angles chart...")

        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        if hasattr(self, 'use_english') and self.use_english:
            fig.suptitle('Piper Robot Joint Angles', fontsize=16, fontweight='bold')
        else:
            fig.suptitle('Piper机械臂关节角度变化', fontsize=16, fontweight='bold')
        
        joint_names = ['Joint 1', 'Joint 2', 'Joint 3', 'Joint 4', 'Joint 5', 'Joint 6']
        joint_cols = ['Joint1', 'Joint2', 'Joint3', 'Joint4', 'Joint5', 'Joint6']
        colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#FFA07A', '#98D8C8', '#F7DC6F']
        
        time_data = joint_data['Time']
        
        for i, (name, col, color) in enumerate(zip(joint_names, joint_cols, colors)):
            if col in joint_data:
                row, col_idx = i // 3, i % 3
                ax = axes[row, col_idx]
                
                angles_deg = np.degrees(joint_data[col])
                ax.plot(time_data, angles_deg, color=color, linewidth=2.5, label=name)

                if hasattr(self, 'use_english') and self.use_english:
                    ax.set_title(f'{name} Angle', fontsize=14, fontweight='bold')
                    ax.set_xlabel('Time (s)', fontsize=12)
                    ax.set_ylabel('Angle (degrees)', fontsize=12)
                    stats_text = f'Range: {range_angle:.1f}°\nMin: {min_angle:.1f}°\nMax: {max_angle:.1f}°'
                else:
                    ax.set_title(f'{name} 角度变化', fontsize=14, fontweight='bold')
                    ax.set_xlabel('时间 (s)', fontsize=12)
                    ax.set_ylabel('角度 (°)', fontsize=12)
                    stats_text = f'范围: {range_angle:.1f}°\n最小: {min_angle:.1f}°\n最大: {max_angle:.1f}°'

                ax.grid(True, alpha=0.3)

                # 添加统计信息
                min_angle = np.min(angles_deg)
                max_angle = np.max(angles_deg)
                range_angle = max_angle - min_angle
                ax.text(0.02, 0.98, stats_text,
                       transform=ax.transAxes, verticalalignment='top',
                       bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.8))
                
        plt.tight_layout()
        
        if save_path is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            save_path = os.path.join(self.data_dir, f'joint_angles_chart_{timestamp}.png')
        
        plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
        print(f"✅ 关节角度图表已保存: {save_path}")
        plt.show()
        
        return save_path
        
    def generate_3d_trajectory_chart(self, pose_data, save_path=None):
        """生成3D轨迹图表"""
        print("📊 生成3D轨迹图表...")
        
        fig = plt.figure(figsize=(12, 10))
        ax = fig.add_subplot(111, projection='3d')
        
        x_data = pose_data['X']
        y_data = pose_data['Y']
        z_data = pose_data['Z']
        
        # 绘制轨迹
        ax.plot(x_data, y_data, z_data, 'b-', linewidth=3, label='轨迹路径', alpha=0.8)
        
        # 标记起点和终点
        ax.scatter(x_data[0], y_data[0], z_data[0], 
                  color='green', s=200, label='起点', marker='o', edgecolors='black', linewidth=2)
        ax.scatter(x_data[-1], y_data[-1], z_data[-1], 
                  color='red', s=200, label='终点', marker='s', edgecolors='black', linewidth=2)
        
        # 添加轨迹点
        step = max(1, len(x_data) // 20)  # 显示20个中间点
        ax.scatter(x_data[::step], y_data[::step], z_data[::step], 
                  color='orange', s=30, alpha=0.6, label='轨迹点')
        
        ax.set_xlabel('X 坐标 (m)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Y 坐标 (m)', fontsize=12, fontweight='bold')
        ax.set_zlabel('Z 坐标 (m)', fontsize=12, fontweight='bold')
        ax.set_title('末端执行器3D轨迹', fontsize=16, fontweight='bold')
        ax.legend(fontsize=12)
        
        # 设置相等的坐标轴比例
        max_range = np.array([x_data, y_data, z_data]).max()
        min_range = np.array([x_data, y_data, z_data]).min()
        ax.set_xlim(min_range, max_range)
        ax.set_ylim(min_range, max_range)
        ax.set_zlim(min_range, max_range)
        
        if save_path is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            save_path = os.path.join(self.data_dir, f'3d_trajectory_chart_{timestamp}.png')
        
        plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
        print(f"✅ 3D轨迹图表已保存: {save_path}")
        plt.show()
        
        return save_path
        
    def generate_yz_trajectory_chart(self, pose_data, save_path=None):
        """生成Y-Z平面轨迹图表"""
        print("📊 生成Y-Z平面轨迹图表...")
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))
        fig.suptitle('末端执行器轨迹分析', fontsize=16, fontweight='bold')
        
        y_data = np.array(pose_data['Y'])
        z_data = np.array(pose_data['Z'])
        
        # Y-Z平面轨迹
        ax1.plot(y_data, z_data, 'b-', linewidth=3, label='实际轨迹', alpha=0.8)
        ax1.scatter(y_data[0], z_data[0], color='green', s=150, label='起点', marker='o', edgecolors='black', linewidth=2)
        ax1.scatter(y_data[-1], z_data[-1], color='red', s=150, label='终点', marker='s', edgecolors='black', linewidth=2)
        
        # 添加理想圆弧对比
        center_y = (np.max(y_data) + np.min(y_data)) / 2
        center_z = (np.max(z_data) + np.min(z_data)) / 2
        radius = (np.max(y_data) - np.min(y_data)) / 2
        
        theta = np.linspace(0, 2*np.pi, 100)
        ideal_y = center_y + radius * np.cos(theta)
        ideal_z = center_z + radius * np.sin(theta)
        ax1.plot(ideal_y, ideal_z, 'r--', alpha=0.5, linewidth=2, label='理想圆弧')
        
        ax1.set_xlabel('Y 坐标 (m)', fontsize=12, fontweight='bold')
        ax1.set_ylabel('Z 坐标 (m)', fontsize=12, fontweight='bold')
        ax1.set_title('Y-Z平面轨迹', fontsize=14, fontweight='bold')
        ax1.legend(fontsize=12)
        ax1.grid(True, alpha=0.3)
        ax1.axis('equal')
        
        # 轨迹误差分析
        distances = []
        for i in range(len(y_data)):
            dist = np.sqrt((y_data[i] - center_y)**2 + (z_data[i] - center_z)**2)
            distances.append(dist)
        
        distances = np.array(distances)
        errors = distances - radius
        
        time_data = pose_data['Time']
        ax2.plot(time_data, errors * 1000, 'r-', linewidth=2, label='轨迹误差')  # 转换为mm
        ax2.axhline(y=0, color='k', linestyle='--', alpha=0.5)
        ax2.set_xlabel('时间 (s)', fontsize=12, fontweight='bold')
        ax2.set_ylabel('轨迹误差 (mm)', fontsize=12, fontweight='bold')
        ax2.set_title('轨迹精度分析', fontsize=14, fontweight='bold')
        ax2.grid(True, alpha=0.3)
        
        # 添加统计信息
        rms_error = np.sqrt(np.mean(errors**2)) * 1000
        max_error = np.max(np.abs(errors)) * 1000
        ax2.text(0.02, 0.98, f'RMS误差: {rms_error:.2f}mm\n最大误差: {max_error:.2f}mm', 
                transform=ax2.transAxes, verticalalignment='top',
                bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.8))
        
        plt.tight_layout()
        
        if save_path is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            save_path = os.path.join(self.data_dir, f'yz_trajectory_chart_{timestamp}.png')
        
        plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
        print(f"✅ Y-Z轨迹图表已保存: {save_path}")
        plt.show()
        
        return save_path
        
    def generate_velocity_chart(self, joint_data, save_path=None):
        """生成关节角速度图表"""
        print("📊 生成关节角速度图表...")
        
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('Piper机械臂关节角速度分析', fontsize=16, fontweight='bold')
        
        joint_names = ['Joint 1', 'Joint 2', 'Joint 3', 'Joint 4', 'Joint 5', 'Joint 6']
        joint_cols = ['Joint1', 'Joint2', 'Joint3', 'Joint4', 'Joint5', 'Joint6']
        colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#FFA07A', '#98D8C8', '#F7DC6F']
        
        time_data = np.array(joint_data['Time'])
        
        for i, (name, col, color) in enumerate(zip(joint_names, joint_cols, colors)):
            if col in joint_data:
                row, col_idx = i // 3, i % 3
                ax = axes[row, col_idx]
                
                # 计算角速度
                angles = np.array(joint_data[col])
                velocities = np.gradient(angles, time_data)
                velocities_deg = np.degrees(velocities)
                
                ax.plot(time_data, velocities_deg, color=color, linewidth=2.5)
                ax.set_title(f'{name} 角速度', fontsize=14, fontweight='bold')
                ax.set_xlabel('时间 (s)', fontsize=12)
                ax.set_ylabel('角速度 (°/s)', fontsize=12)
                ax.grid(True, alpha=0.3)
                
                # 添加统计信息
                max_vel = np.max(np.abs(velocities_deg))
                rms_vel = np.sqrt(np.mean(velocities_deg**2))
                ax.text(0.02, 0.98, f'最大: {max_vel:.1f}°/s\nRMS: {rms_vel:.1f}°/s', 
                       transform=ax.transAxes, verticalalignment='top',
                       bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.8))
                
        plt.tight_layout()
        
        if save_path is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            save_path = os.path.join(self.data_dir, f'joint_velocities_chart_{timestamp}.png')
        
        plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
        print(f"✅ 关节角速度图表已保存: {save_path}")
        plt.show()
        
        return save_path

    def generate_all_charts(self):
        """生成所有图表"""
        print("\n🚀 开始生成所有图表...")

        # 加载数据
        joint_data, pose_data = self.get_latest_data()
        if joint_data is None or pose_data is None:
            return []

        saved_files = []
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        try:
            # 1. 关节角度图表
            path1 = self.generate_joint_angles_chart(joint_data,
                os.path.join(self.data_dir, f'joint_angles_chart_{timestamp}.png'))
            saved_files.append(path1)

            # 2. 3D轨迹图表
            path2 = self.generate_3d_trajectory_chart(pose_data,
                os.path.join(self.data_dir, f'3d_trajectory_chart_{timestamp}.png'))
            saved_files.append(path2)

            # 3. Y-Z轨迹图表
            path3 = self.generate_yz_trajectory_chart(pose_data,
                os.path.join(self.data_dir, f'yz_trajectory_chart_{timestamp}.png'))
            saved_files.append(path3)

            # 4. 角速度图表
            path4 = self.generate_velocity_chart(joint_data,
                os.path.join(self.data_dir, f'joint_velocities_chart_{timestamp}.png'))
            saved_files.append(path4)

            print(f"\n🎉 所有图表生成完成！共生成 {len(saved_files)} 个图表文件")
            print("📁 保存位置:")
            for file_path in saved_files:
                print(f"  {os.path.basename(file_path)}")

        except Exception as e:
            print(f"❌ 生成图表时出错: {e}")

        return saved_files

    def run_interactive_mode(self):
        """运行交互模式"""
        while True:
            print("\n" + "="*60)
            print("🎨 Piper机械臂图表生成器")
            print("="*60)
            print("1. 使用现有数据生成所有图表")
            print("2. 生成关节角度图表")
            print("3. 生成3D轨迹图表")
            print("4. 生成Y-Z轨迹图表")
            print("5. 生成关节角速度图表")
            print("0. 退出")
            print("-"*60)

            try:
                choice = input("请选择操作 (0-5): ").strip()

                if choice == '0':
                    print("👋 退出图表生成器")
                    break
                elif choice == '1':
                    self.generate_all_charts()
                elif choice == '2':
                    joint_data, _ = self.get_latest_data()
                    if joint_data:
                        self.generate_joint_angles_chart(joint_data)
                elif choice == '3':
                    _, pose_data = self.get_latest_data()
                    if pose_data:
                        self.generate_3d_trajectory_chart(pose_data)
                elif choice == '4':
                    _, pose_data = self.get_latest_data()
                    if pose_data:
                        self.generate_yz_trajectory_chart(pose_data)
                elif choice == '5':
                    joint_data, _ = self.get_latest_data()
                    if joint_data:
                        self.generate_velocity_chart(joint_data)
                else:
                    print("❌ 无效选择，请重试")

            except KeyboardInterrupt:
                print("\n👋 退出图表生成器")
                break
            except Exception as e:
                print(f"❌ 操作失败: {e}")

def main():
    """主函数"""
    try:
        generator = ChartGenerator()

        # 检查是否有命令行参数
        if len(sys.argv) > 1:
            if sys.argv[1] == '--all':
                # 直接生成所有图表
                generator.generate_all_charts()
            else:
                print("用法:")
                print("  python3 generate_charts.py           # 交互模式")
                print("  python3 generate_charts.py --all     # 生成所有图表")
        else:
            # 交互模式
            generator.run_interactive_mode()

    except KeyboardInterrupt:
        print("\n👋 程序被用户中断")
    except Exception as e:
        print(f"❌ 程序错误: {e}")

if __name__ == '__main__':
    main()
