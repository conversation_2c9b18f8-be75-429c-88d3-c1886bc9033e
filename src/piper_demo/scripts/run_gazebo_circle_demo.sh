#!/bin/bash

# Piper机械臂Gazebo圆弧轨迹演示启动脚本

echo "=== Piper Gazebo Circle Demo ==="
echo "This script will:"
echo "1. Launch Gazebo with Piper robot"
echo "2. Start MoveIt planning"
echo "3. Execute circle trajectory demo"
echo ""

# 检查ROS环境
if [ -z "$ROS_PACKAGE_PATH" ]; then
    echo "Error: ROS environment not set up!"
    echo "Please run: source devel/setup.bash"
    exit 1
fi

# 设置工作目录
cd /home/<USER>/piper_ros
source devel/setup.bash

echo "Starting Gazebo demo in 3 seconds..."
sleep 3

# 启动Gazebo MoveIt demo
echo "Launching Gazebo MoveIt demo..."
roslaunch piper_no_gripper_moveit demo_gazebo.launch &

# 等待Gazebo和MoveIt启动
echo "Waiting for Gazebo and MoveIt to start (30 seconds)..."
sleep 30

# 运行圆弧演示
echo "Running circle trajectory demo..."
rosrun piper_demo gazebo_circle_demo.py

echo "Demo completed!"
