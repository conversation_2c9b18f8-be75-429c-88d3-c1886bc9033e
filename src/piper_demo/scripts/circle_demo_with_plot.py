#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Piper机械臂圆弧轨迹演示程序 - 带可视化版本
功能：
1. 执行圆弧轨迹
2. 记录关节角度和末端执行器位置
3. 绘制轨迹曲线和关节角度变化图
4. 保存数据和图像
"""

import rospy
import sys
import moveit_commander
from moveit_commander import MoveGroupCommander, PlanningSceneInterface
from geometry_msgs.msg import Pose, PoseStamped, Point
from sensor_msgs.msg import JointState
from copy import deepcopy
import moveit_msgs.msg
import math
import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import time
import os
from datetime import datetime

class CircleDemoWithPlot:
    def __init__(self):
        """初始化带可视化的圆弧演示类"""
        # 初始化moveit_commander和rospy节点
        moveit_commander.roscpp_initialize(sys.argv)
        rospy.init_node('circle_demo_with_plot', anonymous=True)
        
        # 初始化机械臂规划组
        self.arm = MoveGroupCommander('arm')
        self.arm.set_planning_time(10)
        self.arm.set_num_planning_attempts(5)
        self.arm.set_max_velocity_scaling_factor(0.2)  # 降低速度以便更好记录
        self.arm.set_max_acceleration_scaling_factor(0.2)
        
        # 初始化场景
        self.scene = PlanningSceneInterface()
        
        # 设置参考坐标系
        self.arm.set_pose_reference_frame('base_link')
        
        # 获取末端执行器链接名称
        self.end_effector_link = self.arm.get_end_effector_link()
        
        # 数据记录变量
        self.joint_data = []  # 关节角度数据
        self.pose_data = []   # 末端执行器位置数据
        self.time_data = []   # 时间数据
        self.recording = False
        
        # 订阅关节状态
        self.joint_sub = rospy.Subscriber('/joint_states', JointState, self.joint_state_callback)
        self.current_joint_states = None
        
        # 发布轨迹显示
        self.display_pub = rospy.Publisher('/move_group/display_planned_path',
                                         moveit_msgs.msg.DisplayTrajectory,
                                         queue_size=20)
        
        # 创建保存目录
        self.save_dir = os.path.expanduser("~/piper_trajectory_data")
        if not os.path.exists(self.save_dir):
            os.makedirs(self.save_dir)
        
        rospy.loginfo("=== Circle Demo with Plot Initialized ===")
        rospy.loginfo("Planning group: %s", self.arm.get_name())
        rospy.loginfo("End effector: %s", self.end_effector_link)
        rospy.loginfo("Reference frame: %s", self.arm.get_planning_frame())
        rospy.loginfo("Data will be saved to: %s", self.save_dir)
        
        # 等待关节状态
        rospy.loginfo("Waiting for joint states...")
        while self.current_joint_states is None and not rospy.is_shutdown():
            rospy.sleep(0.1)
        rospy.loginfo("Joint states received!")
        
    def joint_state_callback(self, msg):
        """关节状态回调函数"""
        self.current_joint_states = msg
        
        # 如果正在记录数据，保存当前状态
        if self.recording:
            self.record_current_state()
        
    def record_current_state(self):
        """记录当前状态"""
        if self.current_joint_states is None:
            return
            
        # 记录时间
        current_time = rospy.get_time()
        if not self.time_data:
            self.start_time = current_time
            self.time_data.append(0.0)
        else:
            self.time_data.append(current_time - self.start_time)
        
        # 记录关节角度
        joint_names = ['joint1', 'joint2', 'joint3', 'joint4', 'joint5', 'joint6']
        joint_angles = []
        
        for joint_name in joint_names:
            if joint_name in self.current_joint_states.name:
                idx = self.current_joint_states.name.index(joint_name)
                angle = self.current_joint_states.position[idx]
                joint_angles.append(angle)
            else:
                joint_angles.append(0.0)
        
        self.joint_data.append(joint_angles)
        
        # 记录末端执行器位置
        try:
            current_pose = self.arm.get_current_pose().pose
            pose_data = [
                current_pose.position.x,
                current_pose.position.y,
                current_pose.position.z,
                current_pose.orientation.x,
                current_pose.orientation.y,
                current_pose.orientation.z,
                current_pose.orientation.w
            ]
            self.pose_data.append(pose_data)
        except:
            # 如果获取位姿失败，使用上一个值或零值
            if self.pose_data:
                self.pose_data.append(self.pose_data[-1])
            else:
                self.pose_data.append([0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0])
        
    def start_recording(self):
        """开始记录数据"""
        self.joint_data = []
        self.pose_data = []
        self.time_data = []
        self.recording = True
        rospy.loginfo("Started recording trajectory data...")
        
    def stop_recording(self):
        """停止记录数据"""
        self.recording = False
        rospy.loginfo("Stopped recording. Recorded %d data points.", len(self.joint_data))
        
    def print_joint_angles(self, title="Current Joint Angles"):
        """打印当前关节角度"""
        if self.current_joint_states is None:
            rospy.logwarn("No joint states available")
            return
            
        rospy.loginfo("=== %s ===", title)
        joint_names = ['joint1', 'joint2', 'joint3', 'joint4', 'joint5', 'joint6']
        
        for i, joint_name in enumerate(joint_names):
            if joint_name in self.current_joint_states.name:
                idx = self.current_joint_states.name.index(joint_name)
                angle_rad = self.current_joint_states.position[idx]
                angle_deg = math.degrees(angle_rad)
                rospy.loginfo("%s: %.4f rad (%.2f°)", joint_name, angle_rad, angle_deg)
        rospy.loginfo("=" * 40)
        
    def move_to_start_position(self):
        """移动到起始位置"""
        rospy.loginfo("Moving to start position...")
        
        # 设置起始关节角度 (单位：弧度)
        start_joint_angles = [0.0, 0.3, -0.5, 0.0, 0.2, 0.0]
        
        self.arm.set_joint_value_target(start_joint_angles)
        success = self.arm.go(wait=True)
        self.arm.stop()
        
        if success:
            rospy.loginfo("Successfully moved to start position")
            rospy.sleep(2)  # 等待稳定
            self.print_joint_angles("Start Position Joint Angles")
            return True
        else:
            rospy.logerr("Failed to move to start position")
            return False
            
    def generate_circle_waypoints(self, center, radius, num_points=12):
        """生成圆弧轨迹路径点"""
        waypoints = []
        
        # 获取当前位姿作为起始点
        current_pose = self.arm.get_current_pose().pose
        start_pose = deepcopy(current_pose)
        
        rospy.loginfo("Circle parameters:")
        rospy.loginfo("  Center: (%.3f, %.3f, %.3f)", center[0], center[1], center[2])
        rospy.loginfo("  Radius: %.3f m", radius)
        rospy.loginfo("  Points: %d", num_points)
        
        # 生成圆弧上的点 (在YZ平面内) - 完整圆弧
        for i in range(num_points + 1):
            angle = 2 * math.pi * i / num_points  # 完整圆弧 (0 到 2π)
            
            waypoint = deepcopy(start_pose)
            waypoint.position.x = center[0]  # X坐标保持不变
            waypoint.position.y = center[1] + radius * math.cos(angle)
            waypoint.position.z = center[2] + radius * math.sin(angle)
            
            # 保持末端执行器姿态不变
            waypoint.orientation = start_pose.orientation
            
            waypoints.append(waypoint)
            
        rospy.loginfo("Generated %d waypoints for circle trajectory", len(waypoints))
        return waypoints

    def execute_circle_trajectory(self):
        """执行圆弧轨迹"""
        rospy.loginfo("Starting circle trajectory execution...")

        # 获取当前位置
        current_pose = self.arm.get_current_pose().pose
        rospy.loginfo("Current end-effector position:")
        rospy.loginfo("  X: %.3f, Y: %.3f, Z: %.3f",
                     current_pose.position.x,
                     current_pose.position.y,
                     current_pose.position.z)

        # 定义圆弧参数
        center = [current_pose.position.x,
                 current_pose.position.y,
                 current_pose.position.z]
        radius = 0.04  # 4cm半径

        # 生成圆弧路径点
        waypoints = self.generate_circle_waypoints(center, radius, 12)

        # 规划笛卡尔路径
        rospy.loginfo("Planning cartesian path...")
        (plan, fraction) = self.arm.compute_cartesian_path(
            waypoints,   # 路径点
            0.005)       # 步长 (0.5cm)

        rospy.loginfo("Path planning result:")
        rospy.loginfo("  Success rate: %.1f%%", fraction * 100)
        rospy.loginfo("  Trajectory points: %d", len(plan.joint_trajectory.points))

        if fraction > 0.7:  # 如果成功率超过70%
            # 显示规划的轨迹
            display_trajectory = moveit_msgs.msg.DisplayTrajectory()
            display_trajectory.trajectory_start = self.arm.get_current_state()
            display_trajectory.trajectory.append(plan)
            self.display_pub.publish(display_trajectory)

            rospy.loginfo("Executing circle trajectory...")
            self.print_joint_angles("Before Execution")

            # 开始记录数据
            self.start_recording()

            # 执行轨迹
            success = self.arm.execute(plan, wait=True)

            # 停止记录数据
            rospy.sleep(1)  # 等待最后的数据点
            self.stop_recording()

            if success:
                rospy.loginfo("Circle trajectory executed successfully!")
                self.print_joint_angles("After Execution")
                return True
            else:
                rospy.logerr("Failed to execute circle trajectory")
                return False
        else:
            rospy.logwarn("Path planning success rate too low: %.1f%%", fraction * 100)
            return False

    def plot_trajectory_data(self):
        """绘制轨迹数据"""
        if not self.joint_data or not self.pose_data:
            rospy.logwarn("No trajectory data to plot")
            return

        rospy.loginfo("Plotting trajectory data...")

        # 转换数据为numpy数组
        joint_data = np.array(self.joint_data)
        pose_data = np.array(self.pose_data)
        time_data = np.array(self.time_data)

        # 创建时间戳用于文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 1. 绘制关节角度变化
        plt.figure(figsize=(15, 10))

        joint_names = ['Joint 1', 'Joint 2', 'Joint 3', 'Joint 4', 'Joint 5', 'Joint 6']
        colors = ['red', 'blue', 'green', 'orange', 'purple', 'brown']

        for i in range(6):
            plt.subplot(2, 3, i+1)
            plt.plot(time_data, np.degrees(joint_data[:, i]), color=colors[i], linewidth=2)
            plt.title(f'{joint_names[i]} Angle vs Time')
            plt.xlabel('Time (s)')
            plt.ylabel('Angle (degrees)')
            plt.grid(True, alpha=0.3)

        plt.tight_layout()
        joint_plot_file = os.path.join(self.save_dir, f'joint_angles_{timestamp}.png')
        plt.savefig(joint_plot_file, dpi=300, bbox_inches='tight')
        rospy.loginfo("Joint angles plot saved to: %s", joint_plot_file)
        plt.show(block=False)

        # 2. 绘制3D轨迹
        fig = plt.figure(figsize=(12, 8))
        ax = fig.add_subplot(111, projection='3d')

        # 绘制轨迹
        ax.plot(pose_data[:, 0], pose_data[:, 1], pose_data[:, 2],
                'b-', linewidth=3, label='Trajectory')

        # 标记起点和终点
        ax.scatter(pose_data[0, 0], pose_data[0, 1], pose_data[0, 2],
                  color='green', s=100, label='Start')
        ax.scatter(pose_data[-1, 0], pose_data[-1, 1], pose_data[-1, 2],
                  color='red', s=100, label='End')

        ax.set_xlabel('X (m)')
        ax.set_ylabel('Y (m)')
        ax.set_zlabel('Z (m)')
        ax.set_title('End-Effector 3D Trajectory')
        ax.legend()
        ax.grid(True, alpha=0.3)

        trajectory_plot_file = os.path.join(self.save_dir, f'3d_trajectory_{timestamp}.png')
        plt.savefig(trajectory_plot_file, dpi=300, bbox_inches='tight')
        rospy.loginfo("3D trajectory plot saved to: %s", trajectory_plot_file)
        plt.show(block=False)

        # 3. 绘制XY平面轨迹
        plt.figure(figsize=(8, 8))
        plt.plot(pose_data[:, 1], pose_data[:, 2], 'b-', linewidth=3, label='Trajectory')
        plt.scatter(pose_data[0, 1], pose_data[0, 2], color='green', s=100, label='Start')
        plt.scatter(pose_data[-1, 1], pose_data[-1, 2], color='red', s=100, label='End')
        plt.xlabel('Y (m)')
        plt.ylabel('Z (m)')
        plt.title('End-Effector Trajectory (Y-Z Plane)')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.axis('equal')

        xy_plot_file = os.path.join(self.save_dir, f'yz_trajectory_{timestamp}.png')
        plt.savefig(xy_plot_file, dpi=300, bbox_inches='tight')
        rospy.loginfo("Y-Z trajectory plot saved to: %s", xy_plot_file)
        plt.show(block=False)

        # 保持图像显示
        rospy.loginfo("Plots are displayed. Close the plot windows to continue...")
        plt.show()

    def save_data_to_csv(self):
        """保存数据到CSV文件"""
        if not self.joint_data or not self.pose_data:
            rospy.logwarn("No data to save")
            return

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 保存关节数据
        joint_data = np.array(self.joint_data)
        time_data = np.array(self.time_data)

        joint_csv_file = os.path.join(self.save_dir, f'joint_data_{timestamp}.csv')
        with open(joint_csv_file, 'w') as f:
            f.write('Time,Joint1,Joint2,Joint3,Joint4,Joint5,Joint6\n')
            for i in range(len(time_data)):
                f.write(f'{time_data[i]:.4f}')
                for j in range(6):
                    f.write(f',{joint_data[i, j]:.6f}')
                f.write('\n')

        rospy.loginfo("Joint data saved to: %s", joint_csv_file)

        # 保存位姿数据
        pose_data = np.array(self.pose_data)
        pose_csv_file = os.path.join(self.save_dir, f'pose_data_{timestamp}.csv')
        with open(pose_csv_file, 'w') as f:
            f.write('Time,X,Y,Z,Qx,Qy,Qz,Qw\n')
            for i in range(len(time_data)):
                f.write(f'{time_data[i]:.4f}')
                for j in range(7):
                    f.write(f',{pose_data[i, j]:.6f}')
                f.write('\n')

        rospy.loginfo("Pose data saved to: %s", pose_csv_file)

    def run_demo(self):
        """运行演示程序"""
        try:
            rospy.loginfo("=== Starting Circle Demo with Plot ===")

            # 步骤1: 移动到起始位置
            if not self.move_to_start_position():
                return False

            rospy.sleep(2)  # 等待2秒

            # 步骤2: 执行圆弧轨迹
            if not self.execute_circle_trajectory():
                return False

            # 步骤3: 保存数据
            self.save_data_to_csv()

            # 步骤4: 绘制图表
            self.plot_trajectory_data()

            rospy.loginfo("=== Demo completed successfully! ===")
            return True

        except rospy.ROSInterruptException:
            rospy.loginfo("Demo interrupted by user")
            return False
        except Exception as e:
            rospy.logerr("Demo failed with error: %s", str(e))
            return False

    def shutdown(self):
        """关闭演示程序"""
        rospy.loginfo("Shutting down Circle Demo with Plot...")
        moveit_commander.roscpp_shutdown()

def main():
    """主函数"""
    try:
        # 创建演示对象
        demo = CircleDemoWithPlot()

        # 运行演示
        success = demo.run_demo()

        if success:
            rospy.loginfo("Demo completed successfully!")
        else:
            rospy.logerr("Demo failed!")

        # 关闭
        demo.shutdown()

    except KeyboardInterrupt:
        rospy.loginfo("Demo interrupted by user")
    except Exception as e:
        rospy.logerr("Demo failed: %s", str(e))

if __name__ == '__main__':
    main()
