#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Piper机械臂实时数据监控程序
功能：
1. 实时显示关节角度
2. 实时显示末端执行器位置
3. 实时绘制数据曲线
"""

import rospy
import sys
import moveit_commander
from moveit_commander import MoveGroupCommander
from sensor_msgs.msg import JointState
import math
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.animation import FuncAnimation
import threading
from collections import deque

class RealtimeMonitor:
    def __init__(self):
        """初始化实时监控"""
        # 初始化ROS
        rospy.init_node('realtime_monitor', anonymous=True)
        
        # 初始化MoveIt
        moveit_commander.roscpp_initialize(sys.argv)
        self.arm = MoveGroupCommander('arm')
        
        # 数据存储 (使用deque限制数据长度)
        self.max_points = 200  # 最多显示200个数据点
        self.joint_angles = [deque(maxlen=self.max_points) for _ in range(6)]
        self.end_effector_pos = [deque(maxlen=self.max_points) for _ in range(3)]
        self.timestamps = deque(maxlen=self.max_points)
        
        # 当前状态
        self.current_joint_states = None
        self.start_time = None
        
        # 订阅关节状态
        self.joint_sub = rospy.Subscriber('/joint_states', JointState, self.joint_state_callback)
        
        # 设置matplotlib为非阻塞模式
        plt.ion()
        
        # 创建图形
        self.setup_plots()
        
        rospy.loginfo("=== Realtime Monitor Initialized ===")
        rospy.loginfo("Waiting for joint states...")
        
        # 等待关节状态
        while self.current_joint_states is None and not rospy.is_shutdown():
            rospy.sleep(0.1)
        rospy.loginfo("Joint states received!")
        
        # 设置起始时间
        self.start_time = rospy.get_time()
        
    def joint_state_callback(self, msg):
        """关节状态回调函数"""
        self.current_joint_states = msg
        self.update_data()
        
    def update_data(self):
        """更新数据"""
        if self.current_joint_states is None or self.start_time is None:
            return
            
        # 更新时间
        current_time = rospy.get_time() - self.start_time
        self.timestamps.append(current_time)
        
        # 更新关节角度
        joint_names = ['joint1', 'joint2', 'joint3', 'joint4', 'joint5', 'joint6']
        for i, joint_name in enumerate(joint_names):
            if joint_name in self.current_joint_states.name:
                idx = self.current_joint_states.name.index(joint_name)
                angle = math.degrees(self.current_joint_states.position[idx])
                self.joint_angles[i].append(angle)
            else:
                self.joint_angles[i].append(0.0)
        
        # 更新末端执行器位置
        try:
            current_pose = self.arm.get_current_pose().pose
            self.end_effector_pos[0].append(current_pose.position.x)
            self.end_effector_pos[1].append(current_pose.position.y)
            self.end_effector_pos[2].append(current_pose.position.z)
        except:
            # 如果获取失败，使用上一个值或零值
            for i in range(3):
                if self.end_effector_pos[i]:
                    self.end_effector_pos[i].append(self.end_effector_pos[i][-1])
                else:
                    self.end_effector_pos[i].append(0.0)
    
    def setup_plots(self):
        """设置绘图"""
        # 创建主窗口
        self.fig, self.axes = plt.subplots(3, 3, figsize=(15, 12))
        self.fig.suptitle('Piper Robot Real-time Monitor', fontsize=16)
        
        # 关节角度图 (2x3)
        joint_names = ['Joint 1', 'Joint 2', 'Joint 3', 'Joint 4', 'Joint 5', 'Joint 6']
        colors = ['red', 'blue', 'green', 'orange', 'purple', 'brown']
        
        self.joint_lines = []
        for i in range(6):
            row = i // 3
            col = i % 3
            ax = self.axes[row, col]
            line, = ax.plot([], [], color=colors[i], linewidth=2)
            ax.set_title(f'{joint_names[i]} Angle')
            ax.set_xlabel('Time (s)')
            ax.set_ylabel('Angle (degrees)')
            ax.grid(True, alpha=0.3)
            ax.set_ylim(-180, 180)
            self.joint_lines.append(line)
        
        # 末端执行器位置图 (第三行)
        pos_names = ['X Position', 'Y Position', 'Z Position']
        pos_colors = ['cyan', 'magenta', 'yellow']
        
        self.pos_lines = []
        for i in range(3):
            ax = self.axes[2, i]
            line, = ax.plot([], [], color=pos_colors[i], linewidth=2)
            ax.set_title(pos_names[i])
            ax.set_xlabel('Time (s)')
            ax.set_ylabel('Position (m)')
            ax.grid(True, alpha=0.3)
            self.pos_lines.append(line)
        
        plt.tight_layout()
        
    def update_plots(self):
        """更新图形"""
        if not self.timestamps:
            return
            
        times = list(self.timestamps)
        
        # 更新关节角度图
        for i in range(6):
            if self.joint_angles[i]:
                angles = list(self.joint_angles[i])
                self.joint_lines[i].set_data(times, angles)
                
                # 自动调整Y轴范围
                if angles:
                    y_min, y_max = min(angles), max(angles)
                    margin = (y_max - y_min) * 0.1 if y_max != y_min else 10
                    self.axes[i//3, i%3].set_ylim(y_min - margin, y_max + margin)
                
                # 自动调整X轴范围
                if times:
                    self.axes[i//3, i%3].set_xlim(max(0, times[-1] - 30), times[-1] + 1)
        
        # 更新末端执行器位置图
        for i in range(3):
            if self.end_effector_pos[i]:
                positions = list(self.end_effector_pos[i])
                self.pos_lines[i].set_data(times, positions)
                
                # 自动调整Y轴范围
                if positions:
                    y_min, y_max = min(positions), max(positions)
                    margin = (y_max - y_min) * 0.1 if y_max != y_min else 0.01
                    self.axes[2, i].set_ylim(y_min - margin, y_max + margin)
                
                # 自动调整X轴范围
                if times:
                    self.axes[2, i].set_xlim(max(0, times[-1] - 30), times[-1] + 1)
        
        # 刷新图形
        self.fig.canvas.draw()
        self.fig.canvas.flush_events()
        
    def print_current_status(self):
        """打印当前状态"""
        if self.current_joint_states is None:
            return
            
        print("\n" + "="*60)
        print("PIPER ROBOT REAL-TIME STATUS")
        print("="*60)
        
        # 打印关节角度
        joint_names = ['joint1', 'joint2', 'joint3', 'joint4', 'joint5', 'joint6']
        print("Joint Angles:")
        for i, joint_name in enumerate(joint_names):
            if joint_name in self.current_joint_states.name:
                idx = self.current_joint_states.name.index(joint_name)
                angle_rad = self.current_joint_states.position[idx]
                angle_deg = math.degrees(angle_rad)
                print(f"  {joint_name}: {angle_rad:8.4f} rad ({angle_deg:7.2f}°)")
        
        # 打印末端执行器位置
        try:
            current_pose = self.arm.get_current_pose().pose
            print("\nEnd-Effector Position:")
            print(f"  X: {current_pose.position.x:8.4f} m")
            print(f"  Y: {current_pose.position.y:8.4f} m")
            print(f"  Z: {current_pose.position.z:8.4f} m")
            
            print("\nEnd-Effector Orientation:")
            print(f"  Qx: {current_pose.orientation.x:8.4f}")
            print(f"  Qy: {current_pose.orientation.y:8.4f}")
            print(f"  Qz: {current_pose.orientation.z:8.4f}")
            print(f"  Qw: {current_pose.orientation.w:8.4f}")
        except:
            print("\nEnd-Effector Position: Unable to get current pose")
        
        print("="*60)
        
    def run_monitor(self):
        """运行监控"""
        rospy.loginfo("Starting real-time monitor...")
        rospy.loginfo("Press Ctrl+C to stop")
        
        try:
            rate = rospy.Rate(10)  # 10Hz更新频率
            
            while not rospy.is_shutdown():
                # 更新图形
                self.update_plots()
                
                # 每2秒打印一次状态
                if len(self.timestamps) % 20 == 0:  # 10Hz * 2s = 20
                    self.print_current_status()
                
                rate.sleep()
                
        except KeyboardInterrupt:
            rospy.loginfo("Monitor stopped by user")
        except Exception as e:
            rospy.logerr("Monitor failed: %s", str(e))
        finally:
            plt.close('all')
            moveit_commander.roscpp_shutdown()

def main():
    """主函数"""
    try:
        monitor = RealtimeMonitor()
        monitor.run_monitor()
    except KeyboardInterrupt:
        rospy.loginfo("Program interrupted by user")
    except Exception as e:
        rospy.logerr("Program failed: %s", str(e))

if __name__ == '__main__':
    main()
