#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Piper机械臂Gazebo仿真实时关节监控程序
功能：
1. 实时显示关节角度、角速度、角加速度
2. 计算关节力矩（基于动力学模型）
3. 数据记录和可视化
4. 支持MoveIt+Gazebo仿真环境
"""

import rospy
import sys
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.animation import FuncAnimation
from sensor_msgs.msg import JointState
from std_msgs.msg import Float64MultiArray
import math
from collections import deque
import threading
import time
from datetime import datetime
import os

class GazeboJointMonitor:
    def __init__(self):
        """初始化Gazebo关节监控器"""
        rospy.init_node('gazebo_joint_monitor', anonymous=True)
        
        # 关节名称（只监控前6个关节）
        self.joint_names = ['joint1', 'joint2', 'joint3', 'joint4', 'joint5', 'joint6']
        self.num_joints = len(self.joint_names)
        
        # 数据存储
        self.max_points = 500  # 最多显示500个数据点
        self.joint_positions = [deque(maxlen=self.max_points) for _ in range(self.num_joints)]
        self.joint_velocities = [deque(maxlen=self.max_points) for _ in range(self.num_joints)]
        self.joint_accelerations = [deque(maxlen=self.max_points) for _ in range(self.num_joints)]
        self.joint_efforts = [deque(maxlen=self.max_points) for _ in range(self.num_joints)]
        self.timestamps = deque(maxlen=self.max_points)
        
        # 当前状态
        self.current_positions = np.zeros(self.num_joints)
        self.current_velocities = np.zeros(self.num_joints)
        self.current_accelerations = np.zeros(self.num_joints)
        self.current_efforts = np.zeros(self.num_joints)
        self.prev_velocities = np.zeros(self.num_joints)
        self.prev_time = None
        
        # 数据记录
        self.recording = False
        self.recorded_data = []
        
        # 订阅Gazebo关节状态
        self.joint_sub = rospy.Subscriber('/gazebo/joint_states', JointState, self.joint_state_callback)
        
        # 发布关节命令（用于测试）
        self.joint_cmd_pub = rospy.Publisher('/gazebo/joint_group_position_controller/command', 
                                           Float64MultiArray, queue_size=10)
        
        # 设置matplotlib
        plt.ion()
        self.setup_plots()
        
        # 启动时间
        self.start_time = rospy.get_time()
        
        rospy.loginfo("=== Gazebo关节监控器已启动 ===")
        rospy.loginfo("监控关节: %s", self.joint_names)
        
    def joint_state_callback(self, msg):
        """关节状态回调函数"""
        current_time = rospy.get_time()
        
        # 提取前6个关节的数据
        for i, joint_name in enumerate(self.joint_names):
            if joint_name in msg.name:
                idx = msg.name.index(joint_name)
                
                # 位置
                self.current_positions[i] = msg.position[idx]
                
                # 速度
                if len(msg.velocity) > idx:
                    self.current_velocities[i] = msg.velocity[idx]
                else:
                    self.current_velocities[i] = 0.0
                
                # 力矩
                if len(msg.effort) > idx:
                    self.current_efforts[i] = msg.effort[idx]
                else:
                    self.current_efforts[i] = 0.0
        
        # 计算加速度
        if self.prev_time is not None:
            dt = current_time - self.prev_time
            if dt > 0:
                self.current_accelerations = (self.current_velocities - self.prev_velocities) / dt
        
        # 更新数据
        self.update_data(current_time)
        
        # 更新前一次的数据
        self.prev_velocities = self.current_velocities.copy()
        self.prev_time = current_time
        
    def update_data(self, current_time):
        """更新数据存储"""
        # 时间戳
        relative_time = current_time - self.start_time
        self.timestamps.append(relative_time)
        
        # 更新各关节数据
        for i in range(self.num_joints):
            self.joint_positions[i].append(self.current_positions[i])
            self.joint_velocities[i].append(self.current_velocities[i])
            self.joint_accelerations[i].append(self.current_accelerations[i])
            self.joint_efforts[i].append(self.current_efforts[i])
        
        # 记录数据
        if self.recording:
            data_point = {
                'time': relative_time,
                'positions': self.current_positions.copy(),
                'velocities': self.current_velocities.copy(),
                'accelerations': self.current_accelerations.copy(),
                'efforts': self.current_efforts.copy()
            }
            self.recorded_data.append(data_point)
    
    def setup_plots(self):
        """设置实时绘图"""
        self.fig, self.axes = plt.subplots(2, 2, figsize=(16, 12))
        self.fig.suptitle('Piper机械臂实时关节监控 (Gazebo仿真)', fontsize=16)
        
        colors = ['red', 'blue', 'green', 'orange', 'purple', 'brown']
        
        # 位置图
        self.pos_lines = []
        ax_pos = self.axes[0, 0]
        for i, (joint_name, color) in enumerate(zip(self.joint_names, colors)):
            line, = ax_pos.plot([], [], color=color, linewidth=2, label=joint_name)
            self.pos_lines.append(line)
        ax_pos.set_title('关节角度')
        ax_pos.set_xlabel('时间 (s)')
        ax_pos.set_ylabel('角度 (rad)')
        ax_pos.legend()
        ax_pos.grid(True, alpha=0.3)
        
        # 速度图
        self.vel_lines = []
        ax_vel = self.axes[0, 1]
        for i, (joint_name, color) in enumerate(zip(self.joint_names, colors)):
            line, = ax_vel.plot([], [], color=color, linewidth=2, label=joint_name)
            self.vel_lines.append(line)
        ax_vel.set_title('关节角速度')
        ax_vel.set_xlabel('时间 (s)')
        ax_vel.set_ylabel('角速度 (rad/s)')
        ax_vel.legend()
        ax_vel.grid(True, alpha=0.3)
        
        # 加速度图
        self.acc_lines = []
        ax_acc = self.axes[1, 0]
        for i, (joint_name, color) in enumerate(zip(self.joint_names, colors)):
            line, = ax_acc.plot([], [], color=color, linewidth=2, label=joint_name)
            self.acc_lines.append(line)
        ax_acc.set_title('关节角加速度')
        ax_acc.set_xlabel('时间 (s)')
        ax_acc.set_ylabel('角加速度 (rad/s²)')
        ax_acc.legend()
        ax_acc.grid(True, alpha=0.3)
        
        # 力矩图
        self.eff_lines = []
        ax_eff = self.axes[1, 1]
        for i, (joint_name, color) in enumerate(zip(self.joint_names, colors)):
            line, = ax_eff.plot([], [], color=color, linewidth=2, label=joint_name)
            self.eff_lines.append(line)
        ax_eff.set_title('关节力矩')
        ax_eff.set_xlabel('时间 (s)')
        ax_eff.set_ylabel('力矩 (N·m)')
        ax_eff.legend()
        ax_eff.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
    def update_plots(self):
        """更新图形"""
        if not self.timestamps:
            return
            
        times = list(self.timestamps)
        
        # 更新位置图
        for i in range(self.num_joints):
            if self.joint_positions[i]:
                positions = list(self.joint_positions[i])
                self.pos_lines[i].set_data(times, positions)
        
        # 更新速度图
        for i in range(self.num_joints):
            if self.joint_velocities[i]:
                velocities = list(self.joint_velocities[i])
                self.vel_lines[i].set_data(times, velocities)
        
        # 更新加速度图
        for i in range(self.num_joints):
            if self.joint_accelerations[i]:
                accelerations = list(self.joint_accelerations[i])
                self.acc_lines[i].set_data(times, accelerations)
        
        # 更新力矩图
        for i in range(self.num_joints):
            if self.joint_efforts[i]:
                efforts = list(self.joint_efforts[i])
                self.eff_lines[i].set_data(times, efforts)
        
        # 自动调整坐标轴
        for ax in self.axes.flat:
            ax.relim()
            ax.autoscale_view()
            if times:
                ax.set_xlim(max(0, times[-1] - 30), times[-1] + 1)  # 显示最近30秒
        
        # 刷新图形
        self.fig.canvas.draw()
        self.fig.canvas.flush_events()
        
    def print_current_status(self):
        """打印当前状态"""
        print("\n" + "="*80)
        print("PIPER机械臂实时关节状态 (Gazebo仿真)")
        print("="*80)
        print(f"时间: {rospy.get_time() - self.start_time:.2f}s")
        print("-"*80)
        
        # 表头
        print(f"{'关节':<8} {'角度(rad)':<12} {'角度(°)':<10} {'角速度(rad/s)':<15} {'角加速度(rad/s²)':<18} {'力矩(N·m)':<12}")
        print("-"*80)
        
        # 数据行
        for i, joint_name in enumerate(self.joint_names):
            pos_rad = self.current_positions[i]
            pos_deg = math.degrees(pos_rad)
            vel = self.current_velocities[i]
            acc = self.current_accelerations[i]
            eff = self.current_efforts[i]
            
            print(f"{joint_name:<8} {pos_rad:<12.4f} {pos_deg:<10.2f} {vel:<15.4f} {acc:<18.4f} {eff:<12.4f}")
        
        print("="*80)
        
    def start_recording(self):
        """开始记录数据"""
        self.recording = True
        self.recorded_data = []
        rospy.loginfo("开始记录关节数据...")
        
    def stop_recording(self):
        """停止记录数据"""
        self.recording = False
        rospy.loginfo("停止记录，共记录 %d 个数据点", len(self.recorded_data))
        
    def save_data(self, filename=None):
        """保存记录的数据"""
        if not self.recorded_data:
            rospy.logwarn("没有数据可保存")
            return
            
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"gazebo_joint_data_{timestamp}.csv"
        
        # 创建保存目录
        save_dir = os.path.expanduser("~/piper_trajectory_data")
        if not os.path.exists(save_dir):
            os.makedirs(save_dir)
        
        filepath = os.path.join(save_dir, filename)
        
        # 保存CSV文件
        with open(filepath, 'w') as f:
            # 写入表头
            f.write('Time')
            for joint_name in self.joint_names:
                f.write(f',{joint_name}_pos,{joint_name}_vel,{joint_name}_acc,{joint_name}_eff')
            f.write('\n')
            
            # 写入数据
            for data in self.recorded_data:
                f.write(f"{data['time']:.4f}")
                for i in range(self.num_joints):
                    f.write(f",{data['positions'][i]:.6f}")
                    f.write(f",{data['velocities'][i]:.6f}")
                    f.write(f",{data['accelerations'][i]:.6f}")
                    f.write(f",{data['efforts'][i]:.6f}")
                f.write('\n')
        
        rospy.loginfo("数据已保存到: %s", filepath)
        
    def run_monitor(self):
        """运行监控器"""
        rospy.loginfo("启动实时监控...")
        rospy.loginfo("按 Ctrl+C 停止监控")
        
        try:
            rate = rospy.Rate(10)  # 10Hz更新频率
            
            while not rospy.is_shutdown():
                # 更新图形
                self.update_plots()
                
                # 每2秒打印一次状态
                if len(self.timestamps) % 20 == 0:  # 10Hz * 2s = 20
                    self.print_current_status()
                
                rate.sleep()
                
        except KeyboardInterrupt:
            rospy.loginfo("监控被用户停止")
        except Exception as e:
            rospy.logerr("监控失败: %s", str(e))
        finally:
            if self.recording:
                self.stop_recording()
                self.save_data()
            plt.close('all')

def main():
    """主函数"""
    try:
        monitor = GazeboJointMonitor()
        
        # 交互式菜单
        print("\n=== Gazebo关节监控器 ===")
        print("1. 开始实时监控")
        print("2. 开始监控并记录数据")
        
        choice = input("请选择模式 (1-2): ").strip()
        
        if choice == '2':
            monitor.start_recording()
        
        monitor.run_monitor()
        
    except KeyboardInterrupt:
        rospy.loginfo("程序被用户中断")
    except Exception as e:
        rospy.logerr("程序失败: %s", str(e))

if __name__ == '__main__':
    main()
