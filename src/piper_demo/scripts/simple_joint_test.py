#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简单的关节测试程序
测试MoveIt基本功能和关节角度输出
"""

import rospy
import sys
import moveit_commander
from moveit_commander import MoveGroupCommander
from sensor_msgs.msg import JointState
import math

class SimpleJointTest:
    def __init__(self):
        """初始化"""
        # 初始化moveit_commander和rospy节点
        moveit_commander.roscpp_initialize(sys.argv)
        rospy.init_node('simple_joint_test', anonymous=True)
        
        # 初始化机械臂规划组
        self.arm = MoveGroupCommander('arm')
        self.arm.set_planning_time(5)
        self.arm.set_num_planning_attempts(3)
        
        # 订阅关节状态
        self.joint_sub = rospy.Subscriber('/joint_states', JointState, self.joint_state_callback)
        self.current_joint_states = None
        
        rospy.loginfo("=== Simple Joint Test Initialized ===")
        rospy.loginfo("Planning group: %s", self.arm.get_name())
        rospy.loginfo("Joint names: %s", self.arm.get_active_joints())
        
        # 等待关节状态
        rospy.loginfo("Waiting for joint states...")
        while self.current_joint_states is None and not rospy.is_shutdown():
            rospy.sleep(0.1)
        rospy.loginfo("Joint states received!")
        
    def joint_state_callback(self, msg):
        """关节状态回调函数"""
        self.current_joint_states = msg
        
    def print_joint_angles(self, title="Current Joint Angles"):
        """打印当前关节角度"""
        if self.current_joint_states is None:
            rospy.logwarn("No joint states available")
            return
            
        rospy.loginfo("=== %s ===", title)
        joint_names = ['joint1', 'joint2', 'joint3', 'joint4', 'joint5', 'joint6']
        
        for i, joint_name in enumerate(joint_names):
            if joint_name in self.current_joint_states.name:
                idx = self.current_joint_states.name.index(joint_name)
                angle_rad = self.current_joint_states.position[idx]
                angle_deg = math.degrees(angle_rad)
                rospy.loginfo("%s: %.4f rad (%.2f°)", joint_name, angle_rad, angle_deg)
        rospy.loginfo("=" * 40)
        
    def test_joint_movement(self):
        """测试关节运动"""
        rospy.loginfo("=== Testing Joint Movement ===")
        
        # 打印初始位置
        self.print_joint_angles("Initial Position")
        
        # 测试位置1
        rospy.loginfo("Moving to test position 1...")
        test_angles_1 = [0.5, 0.0, 0.0, 0.0, 0.0, 0.0]
        self.arm.set_joint_value_target(test_angles_1)
        success1 = self.arm.go(wait=True)
        self.arm.stop()
        
        if success1:
            rospy.loginfo("Successfully moved to position 1")
            rospy.sleep(2)
            self.print_joint_angles("Position 1")
        else:
            rospy.logerr("Failed to move to position 1")
            return False
            
        # 测试位置2
        rospy.loginfo("Moving to test position 2...")
        test_angles_2 = [0.0, 0.5, 0.0, 0.0, 0.0, 0.0]
        self.arm.set_joint_value_target(test_angles_2)
        success2 = self.arm.go(wait=True)
        self.arm.stop()
        
        if success2:
            rospy.loginfo("Successfully moved to position 2")
            rospy.sleep(2)
            self.print_joint_angles("Position 2")
        else:
            rospy.logerr("Failed to move to position 2")
            return False
            
        # 回到零位
        rospy.loginfo("Moving back to zero position...")
        zero_angles = [0.0, 0.0, 0.0, 0.0, 0.0, 0.0]
        self.arm.set_joint_value_target(zero_angles)
        success3 = self.arm.go(wait=True)
        self.arm.stop()
        
        if success3:
            rospy.loginfo("Successfully moved to zero position")
            rospy.sleep(2)
            self.print_joint_angles("Zero Position")
        else:
            rospy.logerr("Failed to move to zero position")
            return False
            
        return True
        
    def run_test(self):
        """运行测试"""
        try:
            rospy.loginfo("=== Starting Simple Joint Test ===")
            
            # 测试关节运动
            if self.test_joint_movement():
                rospy.loginfo("=== All tests passed! ===")
                return True
            else:
                rospy.logerr("=== Tests failed! ===")
                return False
                
        except rospy.ROSInterruptException:
            rospy.loginfo("Test interrupted by user")
            return False
        except Exception as e:
            rospy.logerr("Test failed with error: %s", str(e))
            return False
            
    def shutdown(self):
        """关闭"""
        rospy.loginfo("Shutting down Simple Joint Test...")
        moveit_commander.roscpp_shutdown()

def main():
    """主函数"""
    try:
        # 创建测试对象
        test = SimpleJointTest()
        
        # 运行测试
        success = test.run_test()
        
        if success:
            rospy.loginfo("Test completed successfully!")
        else:
            rospy.logerr("Test failed!")
            
        # 关闭
        test.shutdown()
        
    except KeyboardInterrupt:
        rospy.loginfo("Test interrupted by user")
    except Exception as e:
        rospy.logerr("Test failed: %s", str(e))

if __name__ == '__main__':
    main()
