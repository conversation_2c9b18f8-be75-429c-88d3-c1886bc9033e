# Piper机械臂圆弧轨迹演示程序

这个包包含了Piper机械臂的圆弧轨迹演示程序，具有完整的数据记录和可视化功能。

## 功能特性

### 🎯 主要功能
- **圆弧轨迹规划和执行** - 使用MoveIt进行笛卡尔路径规划
- **实时数据记录** - 记录关节角度和末端执行器位置
- **可视化图表** - 生成多种类型的轨迹图表
- **数据导出** - 保存数据为CSV格式
- **实时监控** - 实时显示机械臂状态

### 📊 生成的图表类型
1. **关节角度变化图** - 显示6个关节在轨迹执行过程中的角度变化
2. **3D轨迹图** - 显示末端执行器的三维运动轨迹
3. **Y-Z平面轨迹图** - 显示圆弧轨迹的侧视图
4. **关节角速度图** - 显示各关节的角速度变化

## 程序列表

### 核心演示程序
- `circle_demo_with_plot.py` - 带可视化的圆弧轨迹演示程序
- `gazebo_circle_demo.py` - 基础圆弧轨迹演示程序
- `simple_joint_test.py` - 简单关节测试程序

### 监控和分析工具
- `realtime_monitor.py` - 实时数据监控程序
- `view_trajectory_data.py` - 轨迹数据查看器

### 启动脚本
- `run_circle_demo_with_plot.sh` - 带可视化演示的启动脚本
- `run_gazebo_circle_demo.sh` - 基础演示的启动脚本

## 使用方法

### 1. 环境准备

确保已经编译了工作空间：
```bash
cd /home/<USER>/piper_ros
catkin_make
source devel/setup.bash
```

### 2. 启动MoveIt

首先启动MoveIt demo：
```bash
roslaunch piper_no_gripper_moveit demo.launch
```

### 3. 运行圆弧轨迹演示

#### 方法1: 使用启动脚本（推荐）
```bash
./src/piper_demo/scripts/run_circle_demo_with_plot.sh
```

#### 方法2: 直接运行程序
```bash
rosrun piper_demo circle_demo_with_plot.py
```

### 4. 查看轨迹数据

演示完成后，可以使用数据查看器：
```bash
rosrun piper_demo view_trajectory_data.py
```

### 5. 实时监控（可选）

在另一个终端中启动实时监控：
```bash
rosrun piper_demo realtime_monitor.py
```

## 输出数据

### 数据保存位置
所有数据都保存在 `~/piper_trajectory_data/` 目录中。

### 文件类型
- `joint_data_YYYYMMDD_HHMMSS.csv` - 关节角度数据
- `pose_data_YYYYMMDD_HHMMSS.csv` - 末端执行器位姿数据
- `joint_angles_YYYYMMDD_HHMMSS.png` - 关节角度图表
- `3d_trajectory_YYYYMMDD_HHMMSS.png` - 3D轨迹图表
- `yz_trajectory_YYYYMMDD_HHMMSS.png` - Y-Z平面轨迹图表

### CSV数据格式

#### 关节数据 (joint_data_*.csv)
```
Time,Joint1,Joint2,Joint3,Joint4,Joint5,Joint6
0.0000,0.000061,0.300076,-0.500025,-0.000072,0.199906,-0.000024
...
```

#### 位姿数据 (pose_data_*.csv)
```
Time,X,Y,Z,Qx,Qy,Qz,Qw
0.0000,0.066550,0.000003,0.343310,-0.000045,0.675597,-0.000022,0.737271
...
```

## 程序参数

### 圆弧参数
- **半径**: 4cm (可在代码中修改)
- **点数**: 12个路径点
- **平面**: Y-Z平面内的圆弧
- **速度**: 20%最大速度

### 数据记录
- **频率**: 约100Hz
- **数据点**: 通常1000-2000个数据点
- **时间**: 轨迹执行时间约15-20秒

## 故障排除

### 常见问题

1. **MoveIt未启动**
   ```
   错误: ROS Master未运行
   解决: 先启动 roslaunch piper_no_gripper_moveit demo.launch
   ```

2. **路径规划失败**
   ```
   错误: Path planning success rate too low
   解决: 调整圆弧半径或起始位置
   ```

3. **图形显示问题**
   ```
   错误: 图形窗口无法显示
   解决: 确保X11转发正常，或使用VNC
   ```

### 调试模式

要启用详细日志，可以设置ROS日志级别：
```bash
export ROSCONSOLE_CONFIG_FILE=/path/to/custom_rosconsole.conf
```

## 技术细节

### 依赖包
- `moveit_commander` - MoveIt Python接口
- `matplotlib` - 图形绘制
- `pandas` - 数据处理
- `numpy` - 数值计算

### 坐标系
- **基坐标系**: `base_link`
- **末端执行器**: `link6`
- **规划坐标系**: `dummy_link`

### 轨迹规划
- **规划器**: OMPL (RRTConnect)
- **路径类型**: 笛卡尔路径
- **步长**: 0.5cm
- **成功率阈值**: 70%

## 扩展功能

### 自定义轨迹
可以修改 `generate_circle_waypoints()` 函数来生成其他形状的轨迹：
- 椭圆轨迹
- 直线轨迹
- 螺旋轨迹
- 自定义路径点

### 数据分析
`view_trajectory_data.py` 提供了丰富的数据分析功能：
- 关节角度范围分析
- 轨迹长度计算
- 平均速度计算
- 角速度分析

## 联系信息

如有问题或建议，请联系开发团队。

---

**注意**: 请确保在安全的环境中运行这些程序，并遵守机械臂操作的安全规范。
