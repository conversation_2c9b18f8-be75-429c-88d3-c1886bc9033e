# 🎨 Piper机械臂图表生成操作指南

## 🎯 快速开始

### 方法1：一键生成所有图表（推荐）

```bash
cd /home/<USER>/piper_ros
source devel/setup.bash
python3 src/piper_demo/scripts/generate_charts.py --all
```

### 方法2：交互式选择

```bash
cd /home/<USER>/piper_ros
source devel/setup.bash
python3 src/piper_demo/scripts/generate_charts.py
```

然后选择你需要的图表类型：
- 1: 生成所有图表
- 2: 关节角度图表
- 3: 3D轨迹图表
- 4: Y-Z平面轨迹图表
- 5: 关节角速度图表

## 📊 生成的图表类型

### 1. 关节角度图表 (`joint_angles_chart_*.png`)
- **用途**: 逆运动学分析
- **内容**: 6个关节的角度变化曲线
- **特点**: 
  - 显示角度范围和变化幅度
  - 包含统计信息（最小值、最大值、变化范围）
  - 适合分析关节运动特性

### 2. 3D轨迹图表 (`3d_trajectory_chart_*.png`)
- **用途**: 工作空间分析
- **内容**: 末端执行器的三维运动轨迹
- **特点**:
  - 立体显示轨迹路径
  - 标记起点和终点
  - 显示轨迹点分布

### 3. Y-Z平面轨迹图表 (`yz_trajectory_chart_*.png`)
- **用途**: 轨迹精度分析
- **内容**: 
  - Y-Z平面轨迹对比（实际vs理想）
  - 轨迹误差分析
- **特点**:
  - 显示轨迹精度
  - RMS误差和最大误差统计
  - 适合评估控制精度

### 4. 关节角速度图表 (`joint_velocities_chart_*.png`)
- **用途**: 逆动力学分析
- **内容**: 6个关节的角速度变化
- **特点**:
  - 显示最大角速度和RMS值
  - 适合动力学分析
  - 电机性能评估

## 📁 文件保存位置

所有图表都保存在：`~/piper_trajectory_data/`

文件命名格式：`图表类型_时间戳.png`

## 🔧 如果没有数据怎么办？

### 方法1：运行轨迹演示生成数据

```bash
# 启动MoveIt demo
roslaunch piper_no_gripper_moveit demo.launch

# 在新终端运行轨迹演示
cd /home/<USER>/piper_ros
source devel/setup.bash
rosrun piper_demo circle_demo_with_plot.py
```

### 方法2：使用关节监控器记录数据

```bash
# 启动MoveIt+Gazebo仿真
roslaunch piper_no_gripper_moveit demo_gazebo.launch

# 在新终端运行监控器
cd /home/<USER>/piper_ros
source devel/setup.bash
python3 src/piper_demo/scripts/joint_monitor_with_torque.py
# 选择模式2（监控并记录数据）
```

## 🎨 图表质量设置

生成的图表具有以下特点：
- **分辨率**: 300 DPI（高质量）
- **格式**: PNG（适合论文和报告）
- **尺寸**: 12x8英寸（标准）
- **字体**: 12pt（清晰可读）
- **颜色**: 专业配色方案

## 📋 常见问题

### Q: 中文字体显示问题
A: 系统会显示字体警告，但不影响图表生成。图表中的中文可能显示为方框，但数据和图形完全正常。

### Q: 如何修改图表样式？
A: 编辑 `generate_charts.py` 文件中的matplotlib参数：
```python
plt.rcParams['figure.figsize'] = (12, 8)  # 图表尺寸
plt.rcParams['font.size'] = 12             # 字体大小
colors = ['#FF6B6B', '#4ECDC4', ...]      # 颜色方案
```

### Q: 如何生成特定时间段的图表？
A: 目前使用最新的数据文件。如需特定数据，可以：
1. 备份当前数据文件
2. 运行新的轨迹演示
3. 生成图表

## 🚀 高级用法

### 批量生成图表
```bash
# 生成多组数据的图表
for i in {1..5}; do
    echo "生成第 $i 组图表..."
    python3 src/piper_demo/scripts/generate_charts.py --all
    sleep 2
done
```

### 自定义保存路径
编辑脚本中的 `self.data_dir` 变量来更改保存位置。

## 📊 图表用途建议

### 论文写作
- **关节角度图**: 用于逆运动学章节
- **3D轨迹图**: 用于工作空间分析
- **Y-Z轨迹图**: 用于精度评估
- **角速度图**: 用于动力学分析

### 实验报告
- 所有图表都适合用于实验结果展示
- 建议配合数据表格使用
- 可以添加对比分析

### 技术文档
- 图表可直接用于技术规格说明
- 适合系统性能评估
- 可用于故障诊断参考

---

**提示**: 生成图表前确保有足够的磁盘空间，每个图表文件约300-700KB。
