#include <ros/ros.h>
#include <moveit/move_group_interface/move_group_interface.h>
#include <moveit/robot_trajectory/robot_trajectory.h>
#include <moveit/trajectory_processing/iterative_time_parameterization.h>

int main(int argc, char **argv)
{
    ros::init(argc, argv, "two_segment_trajectory_demo");
    ros::NodeHandle node_handle;
    ros::AsyncSpinner spinner(1);
    spinner.start();

    // 初始化move_group
    moveit::planning_interface::MoveGroupInterface arm("arm");
    
    // 设置速度和加速度缩放因子
    double velScale = 0.3;
    double accScale = 0.3;
    arm.setMaxVelocityScalingFactor(velScale);
    arm.setMaxAccelerationScalingFactor(accScale);

    // 获取当前状态作为起始状态
    moveit::core::RobotStatePtr start_state = arm.getCurrentState();
    const robot_state::JointModelGroup* joint_model_group = start_state->getJointModelGroup(arm.getName());

    // 控制机械臂先回到初始化位置
    arm.setNamedTarget("zero");
    arm.move();
    ros::Duration(1.0).sleep();

    // 获取当前关节位置
    std::vector<double> joint_group_positions;
    start_state->copyJointGroupPositions(joint_model_group, joint_group_positions);

    // 设置第一个目标点 - 左边两个方块中间 (Box_2和Box_3之间)
    arm.setStartStateToCurrentState();
    geometry_msgs::Pose target_pose1;
    target_pose1.orientation.w = 1.0;
    target_pose1.position.x = 0.37;  // Box_2和Box_3的x坐标相同
    target_pose1.position.y = 0.015;  // Box_2和Box_3的y坐标中点 (0.34 + (-0.31))/2
    target_pose1.position.z = 0.15;   // 稍微高于盒子顶部
    
    arm.setPoseTarget(target_pose1);
    
    // 计算第一条轨迹
    moveit::planning_interface::MoveGroupInterface::Plan plan1;
    bool success = (arm.plan(plan1) == moveit::core::MoveItErrorCode::SUCCESS);
    
    if(!success) {
        ROS_ERROR("Failed to plan first trajectory");
        return 1;
    }

    // 更新起始状态为第一段轨迹的终点
    robot_trajectory::RobotTrajectory rt1(arm.getCurrentState()->getRobotModel(), "arm");
    rt1.setRobotTrajectoryMsg(*arm.getCurrentState(), plan1.trajectory_);
    rt1.getLastWayPoint().copyJointGroupPositions(joint_model_group, joint_group_positions);
    
    start_state->setJointGroupPositions(joint_model_group, joint_group_positions);
    arm.setStartState(*start_state);

    // 设置第二个目标点 - 右边的方块 (Box_1)
    geometry_msgs::Pose target_pose2;
    target_pose2.orientation.w = 1.0;
    target_pose2.position.x = 0.38;  // Box_1的x坐标
    target_pose2.position.y = 0.0;   // Box_1的y坐标
    target_pose2.position.z = 0.15;  // 稍微高于盒子顶部
    
    arm.setPoseTarget(target_pose2);
    
    // 计算第二条轨迹
    moveit::planning_interface::MoveGroupInterface::Plan plan2;
    success = (arm.plan(plan2) == moveit::core::MoveItErrorCode::SUCCESS);
    
    if(!success) {
        ROS_ERROR("Failed to plan second trajectory");
        return 1;
    }

    // 连接两条轨迹
    moveit_msgs::RobotTrajectory trajectory;
    trajectory.joint_trajectory.joint_names = plan1.trajectory_.joint_trajectory.joint_names;
    trajectory.joint_trajectory.points = plan1.trajectory_.joint_trajectory.points;
    
    // 添加第二条轨迹的路点（跳过第一个点，因为它与第一条轨迹的最后一个点重复）
    for (size_t j = 1; j < plan2.trajectory_.joint_trajectory.points.size(); j++) {
        trajectory.joint_trajectory.points.push_back(plan2.trajectory_.joint_trajectory.points[j]);
    }

    // 重新计算时间参数
    moveit::planning_interface::MoveGroupInterface::Plan joinedPlan;
    robot_trajectory::RobotTrajectory rt(arm.getCurrentState()->getRobotModel(), "arm");
    rt.setRobotTrajectoryMsg(*arm.getCurrentState(), trajectory);
    trajectory_processing::IterativeParabolicTimeParameterization iptp;
    iptp.computeTimeStamps(rt, velScale, accScale);

    rt.getRobotTrajectoryMsg(trajectory);
    joinedPlan.trajectory_ = trajectory;

    // 执行连接后的轨迹
    if (!arm.execute(joinedPlan)) {
        ROS_ERROR("Failed to execute plan");
        return 1;
    }

    ros::Duration(1.0).sleep();
    ROS_INFO("Finished");

    // 控制机械臂回到初始化位置
    arm.setNamedTarget("zero");
    arm.move();
    ros::Duration(1.0).sleep();

    ros::shutdown();
    return 0;
}
