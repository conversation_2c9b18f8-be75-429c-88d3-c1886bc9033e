<launch>

    <!-- 将关节控制器的配置参数加载到参数服务器中 -->
    <rosparam file="$(find piper_gazebo)/config/piper_gazebo_control.yaml" command="load"/>

    <!-- 加载controllers -->
    <node name="controller_spawner" pkg="controller_manager" type="spawner" respawn="false"
          output="screen" ns="/gazebo" args="joint_state_controller
                                          joint1_position_controller
                                          joint2_position_controller
                                          joint3_position_controller
                                          joint4_position_controller
                                          joint5_position_controller
                                          joint6_position_controller
                                          joint7_position_controller
                                          joint8_position_controller"/>

    <!-- 运行robot_state_publisher节点，发布tf  -->
    <!-- <node name="robot_state_publisher" pkg="robot_state_publisher" type="robot_state_publisher"
          respawn="false" output="screen">
        <remap from="/joint_states" to="/gazebo/joint_states" />
    </node> -->
    <node name="joint_states_ctrl" 
          pkg="piper_gazebo" 
          type="joint_states_ctrl.py"
          output="screen" 
          launch-prefix="python3" />
</launch>
