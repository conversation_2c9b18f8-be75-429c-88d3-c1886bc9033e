<?xml version="1.0" encoding="utf-8"?>
<robot name="piper_with_go1"
  xmlns:xacro="http://ros.org/wiki/xacro">

  <!-- Go1 Base -->
  <link name="world"/>

  <!-- Go1 Base Link (Main Body) -->
  <link name="go1_base_link">
    <inertial>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <mass value="12.0"/>
      <inertia ixx="0.1" ixy="0" ixz="0" iyy="0.4" iyz="0" izz="0.4"/>
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <geometry>
        <box size="0.366 0.094 0.114"/>
      </geometry>
      <material name="go1_body_color">
        <color rgba="0.2 0.2 0.2 1.0"/>
      </material>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <geometry>
        <box size="0.366 0.094 0.114"/>
      </geometry>
    </collision>
  </link>

  <!-- Fixed joint connecting world to Go1 base -->
  <joint name="world_to_go1" type="fixed">
    <parent link="world"/>
    <child link="go1_base_link"/>
    <origin xyz="0 0 0.32" rpy="0 0 0"/>
  </joint>

  <!-- Arm Mounting Point -->
  <link name="arm_mount_link">
    <inertial>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <mass value="0.5"/>
      <inertia ixx="0.001" ixy="0" ixz="0" iyy="0.001" iyz="0" izz="0.001"/>
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <geometry>
        <box size="0.1 0.1 0.05"/>
      </geometry>
      <material name="mount_color">
        <color rgba="0.8 0.8 0.8 1.0"/>
      </material>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <geometry>
        <box size="0.1 0.1 0.05"/>
      </geometry>
    </collision>
  </link>

  <joint name="arm_mount_joint" type="fixed">
    <parent link="go1_base_link"/>
    <child link="arm_mount_link"/>
    <origin xyz="0.05 0 0.082" rpy="0 0 0"/>
  </joint>

  <!-- 原始Piper机械臂 - 直接复制自piper_no_gripper_description.xacro -->
  <link name="base_link">
    <inertial>
      <origin xyz="-0.00473641164191482 2.56829134630247E-05 0.041451518036016" rpy="0 0 0" />
      <mass value="1.02" />
      <inertia ixx="0.00267433" ixy="-0.00000073" ixz="-0.00017389" iyy="0.00282612" iyz="0.0000004" izz="0.00089624" />
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://piper_description/meshes/base_link.STL" />
      </geometry>
      <material name="">
        <color rgba="0.792156862745098 0.819607843137255 0.933333333333333 1" />
      </material>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://piper_description/meshes/base_link.STL" />
      </geometry>
    </collision>
  </link>

  <!-- Connect arm base to Go1 mount -->
  <joint name="go1_to_arm_base" type="fixed">
    <parent link="arm_mount_link"/>
    <child link="base_link"/>
    <origin xyz="0 0 0.025" rpy="0 0 0"/>
  </joint>

  <link name="link1">
    <inertial>
      <origin xyz="0.000121504734057468 0.000104632162460536 -0.00438597309559853" rpy="0 0 0" />
      <mass value="0.71" />
      <inertia ixx="0.00048916" ixy="-0.00000036" ixz="-0.00000224" iyy="0.00040472" iyz="-0.00000242" izz="0.00043982" />
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://piper_description/meshes/link1.STL" />
      </geometry>
      <material name="">
        <color rgba="0.792156862745098 0.819607843137255 0.933333333333333 1" />
      </material>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://piper_description/meshes/link1.STL" />
      </geometry>
    </collision>
  </link>

  <joint name="joint1" type="revolute">
    <origin xyz="0 0 0.123" rpy="0 0 0" />
    <parent link="base_link" />
    <child link="link1" />
    <axis xyz="0 0 1" />
    <limit lower="-2.618" upper="2.168" effort="100" velocity="5" />
  </joint>

  <link name="link2">
    <inertial>
      <origin xyz="0.198666145229743 -0.010926924140076 0.00142121714502687" rpy="0 0 0" />
      <mass value="1.17" />
      <inertia ixx="0.00116918" ixy="-0.00180037" ixz="0.00025146" iyy="0.06785384" iyz="-0.00000455" izz="0.06774489" />
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://piper_description/meshes/link2.STL" />
      </geometry>
      <material name="">
        <color rgba="0.792156862745098 0.819607843137255 0.933333333333333 1" />
      </material>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://piper_description/meshes/link2.STL" />
      </geometry>
    </collision>
  </link>

  <joint name="joint2" type="revolute">
    <origin xyz="0 0 0" rpy="1.5708 -0.1359 -3.1416" />
    <parent link="link1" />
    <child link="link2" />
    <axis xyz="0 0 1" />
    <limit lower="0" upper="3.14" effort="100" velocity="5" />
  </joint>

  <link name="link3">
    <inertial>
      <origin xyz="-0.0202737662122021 -0.133914995944595 -0.000458682652737356" rpy="0 0 0" />
      <mass value="0.5" />
      <inertia ixx="0.01361711" ixy="0.00165794" ixz="-0.00000048" iyy="0.00045024" iyz="-0.00000045" izz="0.01380322" />
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://piper_description/meshes/link3.STL" />
      </geometry>
      <material name="">
        <color rgba="0.792156862745098 0.819607843137255 0.933333333333333 1" />
      </material>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://piper_description/meshes/link3.STL" />
      </geometry>
    </collision>
  </link>

  <joint name="joint3" type="revolute">
    <origin xyz="0.4 0 0" rpy="0 0 0" />
    <parent link="link2" />
    <child link="link3" />
    <axis xyz="0 0 1" />
    <limit lower="-2.618" upper="2.618" effort="100" velocity="5" />
  </joint>

  <link name="link4">
    <inertial>
      <origin xyz="0.000121504734057468 0.000104632162460536 -0.00438597309559853" rpy="0 0 0" />
      <mass value="0.71" />
      <inertia ixx="0.00048916" ixy="-0.00000036" ixz="-0.00000224" iyy="0.00040472" iyz="-0.00000242" izz="0.00043982" />
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://piper_description/meshes/link4.STL" />
      </geometry>
      <material name="">
        <color rgba="0.792156862745098 0.819607843137255 0.933333333333333 1" />
      </material>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://piper_description/meshes/link4.STL" />
      </geometry>
    </collision>
  </link>

  <joint name="joint4" type="revolute">
    <origin xyz="0 -0.28 0" rpy="1.5708 0 0" />
    <parent link="link3" />
    <child link="link4" />
    <axis xyz="0 0 1" />
    <limit lower="-2.618" upper="2.618" effort="100" velocity="5" />
  </joint>

  <link name="link5">
    <inertial>
      <origin xyz="0.000121504734057468 0.000104632162460536 -0.00438597309559853" rpy="0 0 0" />
      <mass value="0.71" />
      <inertia ixx="0.00048916" ixy="-0.00000036" ixz="-0.00000224" iyy="0.00040472" iyz="-0.00000242" izz="0.00043982" />
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://piper_description/meshes/link5.STL" />
      </geometry>
      <material name="">
        <color rgba="0.792156862745098 0.819607843137255 0.933333333333333 1" />
      </material>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://piper_description/meshes/link5.STL" />
      </geometry>
    </collision>
  </link>

  <joint name="joint5" type="revolute">
    <origin xyz="0 0 0.123" rpy="-1.5708 0 0" />
    <parent link="link4" />
    <child link="link5" />
    <axis xyz="0 0 1" />
    <limit lower="-2.618" upper="2.618" effort="100" velocity="5" />
  </joint>

  <link name="link6">
    <inertial>
      <origin xyz="0.000121504734057468 0.000104632162460536 -0.00438597309559853" rpy="0 0 0" />
      <mass value="0.71" />
      <inertia ixx="0.00048916" ixy="-0.00000036" ixz="-0.00000224" iyy="0.00040472" iyz="-0.00000242" izz="0.00043982" />
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://piper_description/meshes/link6.STL" />
      </geometry>
      <material name="">
        <color rgba="0.792156862745098 0.819607843137255 0.933333333333333 1" />
      </material>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://piper_description/meshes/link6.STL" />
      </geometry>
    </collision>
  </link>

  <joint name="joint6" type="revolute">
    <origin xyz="0 0 0.123" rpy="1.5708 0 0" />
    <parent link="link5" />
    <child link="link6" />
    <axis xyz="0 0 1" />
    <limit lower="-2.618" upper="2.618" effort="100" velocity="5" />
  </joint>

  <!-- Transmission elements for Gazebo -->
  <xacro:macro name="transmission_block" params="tran_name joint_name motor_name">
    <transmission name="${tran_name}">
      <type>transmission_interface/SimpleTransmission</type>
      <joint name="${joint_name}">
        <hardwareInterface>hardware_interface/PositionJointInterface</hardwareInterface>
      </joint>
      <actuator name="${motor_name}">
        <hardwareInterface>hardware_interface/PositionJointInterface</hardwareInterface>
        <mechanicalReduction>1</mechanicalReduction>
      </actuator>
    </transmission>
  </xacro:macro>

  <xacro:transmission_block tran_name="tran1" joint_name="joint1" motor_name="motor1"/>
  <xacro:transmission_block tran_name="tran2" joint_name="joint2" motor_name="motor2"/>
  <xacro:transmission_block tran_name="tran3" joint_name="joint3" motor_name="motor3"/>
  <xacro:transmission_block tran_name="tran4" joint_name="joint4" motor_name="motor4"/>
  <xacro:transmission_block tran_name="tran5" joint_name="joint5" motor_name="motor5"/>
  <xacro:transmission_block tran_name="tran6" joint_name="joint6" motor_name="motor6"/>

  <!-- Gazebo plugin -->
  <gazebo>
    <plugin name="gazebo_ros_control" filename="libgazebo_ros_control.so">
      <robotNamespace>/piper_go1</robotNamespace>
      <legacyModeNS>true</legacyModeNS>
    </plugin>
  </gazebo>

</robot>
