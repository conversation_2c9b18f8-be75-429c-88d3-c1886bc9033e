<?xml version="1.0" encoding="utf-8"?>
<robot name="piper_go1_simple"
  xmlns:xacro="http://ros.org/wiki/xacro">

  <!-- World Link -->
  <link name="world"/>

  <!-- Go1 Base Link (Main Body) -->
  <link name="go1_base_link">
    <inertial>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <mass value="12.0"/>
      <inertia ixx="0.1" ixy="0" ixz="0" iyy="0.4" iyz="0" izz="0.4"/>
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <geometry>
        <box size="0.366 0.094 0.114"/>
      </geometry>
      <material name="go1_body_color">
        <color rgba="0.2 0.2 0.2 1.0"/>
      </material>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <geometry>
        <box size="0.366 0.094 0.114"/>
      </geometry>
    </collision>
  </link>

  <!-- Fixed joint connecting world to Go1 base -->
  <joint name="world_to_go1" type="fixed">
    <parent link="world"/>
    <child link="go1_base_link"/>
    <origin xyz="0 0 0.32" rpy="0 0 0"/>
  </joint>

  <!-- Arm Mounting Point -->
  <link name="arm_mount_link">
    <inertial>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <mass value="0.5"/>
      <inertia ixx="0.001" ixy="0" ixz="0" iyy="0.001" iyz="0" izz="0.001"/>
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <geometry>
        <box size="0.1 0.1 0.05"/>
      </geometry>
      <material name="mount_color">
        <color rgba="0.8 0.8 0.8 1.0"/>
      </material>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <geometry>
        <box size="0.1 0.1 0.05"/>
      </geometry>
    </collision>
  </link>

  <joint name="arm_mount_joint" type="fixed">
    <parent link="go1_base_link"/>
    <child link="arm_mount_link"/>
    <origin xyz="0.05 0 0.082" rpy="0 0 0"/>
  </joint>

  <!-- Piper Arm Base Link - 使用简单几何体 -->
  <link name="base_link">
    <inertial>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <mass value="1.02"/>
      <inertia ixx="0.01" ixy="0" ixz="0" iyy="0.01" iyz="0" izz="0.01"/>
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <geometry>
        <cylinder radius="0.05" length="0.1"/>
      </geometry>
      <material name="piper_material">
        <color rgba="0.8 0.8 0.9 1"/>
      </material>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <geometry>
        <cylinder radius="0.05" length="0.1"/>
      </geometry>
    </collision>
  </link>

  <!-- Connect arm base to Go1 mount -->
  <joint name="go1_to_arm_base" type="fixed">
    <parent link="arm_mount_link"/>
    <child link="base_link"/>
    <origin xyz="0 0 0.025" rpy="0 0 0"/>
  </joint>

  <!-- Link 1 -->
  <link name="link1">
    <inertial>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <mass value="0.71"/>
      <inertia ixx="0.01" ixy="0" ixz="0" iyy="0.01" iyz="0" izz="0.01"/>
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <geometry>
        <cylinder radius="0.04" length="0.12"/>
      </geometry>
      <material name="piper_material"/>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <geometry>
        <cylinder radius="0.04" length="0.12"/>
      </geometry>
    </collision>
  </link>

  <joint name="joint1" type="revolute">
    <origin xyz="0 0 0.05" rpy="0 0 0"/>
    <parent link="base_link"/>
    <child link="link1"/>
    <axis xyz="0 0 1"/>
    <limit lower="-2.618" upper="2.168" effort="100" velocity="5"/>
  </joint>

  <!-- Link 2 -->
  <link name="link2">
    <inertial>
      <origin xyz="0.2 0 0" rpy="0 0 0"/>
      <mass value="1.17"/>
      <inertia ixx="0.01" ixy="0" ixz="0" iyy="0.1" iyz="0" izz="0.1"/>
    </inertial>
    <visual>
      <origin xyz="0.2 0 0" rpy="0 1.57 0"/>
      <geometry>
        <cylinder radius="0.03" length="0.4"/>
      </geometry>
      <material name="piper_material"/>
    </visual>
    <collision>
      <origin xyz="0.2 0 0" rpy="0 1.57 0"/>
      <geometry>
        <cylinder radius="0.03" length="0.4"/>
      </geometry>
    </collision>
  </link>

  <joint name="joint2" type="revolute">
    <origin xyz="0 0 0.06" rpy="0 0 0"/>
    <parent link="link1"/>
    <child link="link2"/>
    <axis xyz="0 1 0"/>
    <limit lower="-1.57" upper="1.57" effort="100" velocity="5"/>
  </joint>

  <!-- Link 3 -->
  <link name="link3">
    <inertial>
      <origin xyz="0.15 0 0" rpy="0 0 0"/>
      <mass value="0.5"/>
      <inertia ixx="0.01" ixy="0" ixz="0" iyy="0.05" iyz="0" izz="0.05"/>
    </inertial>
    <visual>
      <origin xyz="0.15 0 0" rpy="0 1.57 0"/>
      <geometry>
        <cylinder radius="0.025" length="0.3"/>
      </geometry>
      <material name="piper_material"/>
    </visual>
    <collision>
      <origin xyz="0.15 0 0" rpy="0 1.57 0"/>
      <geometry>
        <cylinder radius="0.025" length="0.3"/>
      </geometry>
    </collision>
  </link>

  <joint name="joint3" type="revolute">
    <origin xyz="0.4 0 0" rpy="0 0 0"/>
    <parent link="link2"/>
    <child link="link3"/>
    <axis xyz="0 1 0"/>
    <limit lower="-2.618" upper="2.618" effort="100" velocity="5"/>
  </joint>

  <!-- Link 4 -->
  <link name="link4">
    <inertial>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <mass value="0.71"/>
      <inertia ixx="0.01" ixy="0" ixz="0" iyy="0.01" iyz="0" izz="0.01"/>
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <geometry>
        <cylinder radius="0.02" length="0.12"/>
      </geometry>
      <material name="piper_material"/>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <geometry>
        <cylinder radius="0.02" length="0.12"/>
      </geometry>
    </collision>
  </link>

  <joint name="joint4" type="revolute">
    <origin xyz="0.3 0 0" rpy="0 0 0"/>
    <parent link="link3"/>
    <child link="link4"/>
    <axis xyz="1 0 0"/>
    <limit lower="-2.618" upper="2.618" effort="100" velocity="5"/>
  </joint>

  <!-- Link 5 -->
  <link name="link5">
    <inertial>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <mass value="0.71"/>
      <inertia ixx="0.01" ixy="0" ixz="0" iyy="0.01" iyz="0" izz="0.01"/>
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <geometry>
        <cylinder radius="0.02" length="0.1"/>
      </geometry>
      <material name="piper_material"/>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <geometry>
        <cylinder radius="0.02" length="0.1"/>
      </geometry>
    </collision>
  </link>

  <joint name="joint5" type="revolute">
    <origin xyz="0 0 0.06" rpy="0 0 0"/>
    <parent link="link4"/>
    <child link="link5"/>
    <axis xyz="0 1 0"/>
    <limit lower="-2.618" upper="2.618" effort="100" velocity="5"/>
  </joint>

  <!-- Link 6 (End Effector) -->
  <link name="link6">
    <inertial>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <mass value="0.3"/>
      <inertia ixx="0.001" ixy="0" ixz="0" iyy="0.001" iyz="0" izz="0.001"/>
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <geometry>
        <cylinder radius="0.015" length="0.06"/>
      </geometry>
      <material name="end_effector_color">
        <color rgba="1.0 0.5 0.0 1"/>
      </material>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <geometry>
        <cylinder radius="0.015" length="0.06"/>
      </geometry>
    </collision>
  </link>

  <joint name="joint6" type="revolute">
    <origin xyz="0 0 0.05" rpy="0 0 0"/>
    <parent link="link5"/>
    <child link="link6"/>
    <axis xyz="0 0 1"/>
    <limit lower="-2.618" upper="2.618" effort="100" velocity="5"/>
  </joint>

  <!-- Transmission elements for Gazebo -->
  <xacro:macro name="transmission_block" params="tran_name joint_name motor_name">
    <transmission name="${tran_name}">
      <type>transmission_interface/SimpleTransmission</type>
      <joint name="${joint_name}">
        <hardwareInterface>hardware_interface/PositionJointInterface</hardwareInterface>
      </joint>
      <actuator name="${motor_name}">
        <hardwareInterface>hardware_interface/PositionJointInterface</hardwareInterface>
        <mechanicalReduction>1</mechanicalReduction>
      </actuator>
    </transmission>
  </xacro:macro>

  <xacro:transmission_block tran_name="tran1" joint_name="joint1" motor_name="motor1"/>
  <xacro:transmission_block tran_name="tran2" joint_name="joint2" motor_name="motor2"/>
  <xacro:transmission_block tran_name="tran3" joint_name="joint3" motor_name="motor3"/>
  <xacro:transmission_block tran_name="tran4" joint_name="joint4" motor_name="motor4"/>
  <xacro:transmission_block tran_name="tran5" joint_name="joint5" motor_name="motor5"/>
  <xacro:transmission_block tran_name="tran6" joint_name="joint6" motor_name="motor6"/>

  <!-- Gazebo plugin -->
  <gazebo>
    <plugin name="gazebo_ros_control" filename="libgazebo_ros_control.so">
      <robotNamespace>/piper_go1</robotNamespace>
      <legacyModeNS>true</legacyModeNS>
    </plugin>
  </gazebo>

</robot>
