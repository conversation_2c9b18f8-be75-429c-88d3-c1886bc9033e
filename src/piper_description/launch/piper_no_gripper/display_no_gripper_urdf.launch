<launch>
  <arg name="model" />
  <arg name="rviz_config" default="$(find piper_description)/rviz/piper_no_gripper.rviz" />
  <param
    name="robot_description"
    textfile="$(find piper_description)/urdf/piper_no_gripper_description.urdf" />
  <node
    name="joint_state_publisher_gui"
    pkg="joint_state_publisher_gui"
    type="joint_state_publisher_gui" />
  <node
    name="$(anon robot_state_publisher)"
    pkg="robot_state_publisher"
    type="robot_state_publisher" />
  <node
    name="rviz"
    pkg="rviz"
    type="rviz"
    args="-d $(arg rviz_config)" />
</launch>