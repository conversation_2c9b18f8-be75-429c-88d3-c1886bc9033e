<launch>
  <arg name="model" />
  <arg name="rviz_config" default="$(find piper_description)/rviz/piper_ctrl.rviz" />
  <param
    name="robot_description"
    textfile="$(find piper_description)/urdf/piper_description_new.urdf" />
  
  <!-- <arg name="jnt_stat_source" default="[/joint_states_origin]" />
  <node name="joint_state_publisher" pkg="joint_state_publisher" type="joint_state_publisher">
    <rosparam param="/source_list" subst_value="True">$(arg jnt_stat_source)</rosparam>
  </node> -->

  <node
    name="joint_state_publisher_gui"
    pkg="joint_state_publisher_gui"
    type="joint_state_publisher_gui" />
  <node
    name="robot_state_publisher"
    pkg="robot_state_publisher"
    type="robot_state_publisher" />
  <node
    name="rviz"
    pkg="rviz"
    type="rviz"
    args="-d $(arg rviz_config)" />
</launch>